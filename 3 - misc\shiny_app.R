# Load necessary libraries
library(shiny)
library(dplyr)
library(plotly)
library(datamods)
library(shinyWidgets)


data_client <- readxl::read_xlsx("3 - misc/partner_A_client_data.xlsx")

data_client <- data_client %>%
  mutate(
    GWP = insurance_rate*activity_price/100,
    Commission_OW = commission_rate*GWP,
    GWP_net = GWP - Commission_OW,
    weekofyear = lubridate::week(as.Date(uw_date)),
    year = lubridate::year(as.Date(uw_date))
  )
# Define UI
ui <- fluidPage(
  titlePanel("Year-To-Date Approch illustration"),
  shinyWidgets::panel(
    select_group_ui(
      id = "filters",
      params = list( # list of variables with filters
        list(inputId = "weather_guarantee_type", label = "Guarantee:"),
        list(inputId = "category", label = "Activity:"),
        list(inputId = "trigger_type", label = "Trigger type:"),
        list(inputId = "city", label = "City:"),
        list(inputId = "country", label = "Country:")
      )
    ),
    status = "primary"
  ),
  reactable::reactableOutput(outputId = "table"),
  plotlyOutput("plot")
)

# Define server logic
server <- function(input, output) {

  filtered_data <- select_group_server(
    id = "filters",
    data = reactive(data_client),
    vars = reactive(c("weather_guarantee_type", "category", "trigger_type", "city","country"))
  )
  output$table <- reactable::renderReactable({
    reactable::reactable(filtered_data())
  })

  # Calculate cumulative sum
  result <- reactive({
    filtered_data() %>%
      group_by(year,weekofyear) %>%
      summarise(
        GWP =sum(GWP),
        Commission_OW = sum(Commission_OW)
      ) %>%
      arrange(year, weekofyear) %>%
      group_by(year) %>%
      arrange(year, weekofyear) %>%
      mutate(
        cum_GWP = cumsum(GWP),
        cum_Commission_OW = cumsum(Commission_OW)
      )
  })

  # Create the Plotly plot
  output$plot <- renderPlotly({
    plot_ly(result(), x = ~weekofyear, y = ~cum_GWP, color = ~factor(year), type = 'scatter', mode = 'lines+markers') %>%
      layout(title = 'Cumulative GWP by Week of Year',
             xaxis = list(title = 'Week of Year', tickmode = 'linear', dtick = 3),
             yaxis = list(title = 'Cumulative GWP'),
             legend = list(title = list(text = 'Year')))
  })
}

# Run the application
shinyApp(ui = ui, server = server)
