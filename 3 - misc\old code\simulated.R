#### data simulation


#policy_id <- should be unique
company_name <- c("Orakle weather","Kaban","Cluez","Paris Yatch Marina","We boat","Paris Waterway","DolceVia")
company_name_prob <- c(0.15, 0.15, 0.15, 0.1, 0.25, 0.15, 0.05)

departement <- c("1","2","3","4","6","7","8","9","10","11","12","67","13","14","15","16","17","18","19","2A","21","22","23","79","24","25","26","91",
                 "27","28","29","30","32","33","971","973","2B","31","43","52","5","70","74","65","87","68","92","34","35","36","37","38","39",
                 "974","40","42","44","45","41","46","47","48","49","50","51","972","53","976","54","55","56","57","58","59","60","61","75",
                 "62","63","64","66","69","71","72","73","77","76","93","80","81","82","90","94","95","83", "84","85","86","88","89","78")
n <- length(departement)
probabilities <- rep(1, n)
names(probabilities) <- departement
probabilities["75"] <- 20
probabilities["71"] <- 15
probabilities["41"] <- 15
probabilities["49"] <- 10
probabilities["28"] <- 10

departement_prob <- probabilities / sum(probabilities)

category <- c("Outdoor escape game","Boat rental - cruise","Tourism/travel accommodation","Cycling holidays",
                                    "Boat rental - event","Fireworks/drone shows","Amusement park","Event")
category_prob <- c(0.1,0.25,0.15,0.1,0.1,0.2,0.09,0.01)

trigger_cumulated <- c("Cumulated", "Per hour")
weather_guarantee_type <- c("Rain", "Wind", "Rain and Wind")
prob_weather_guarantee_type <- c(0.8,0.15,0.05)
uw_date <- seq(as.Date("2021-01-01"),as.Date("2024-07-04"),by = 1)
uw_date = uw_date[!(month(uw_date) == 2 & day(uw_date) == 29)]
#activity_start_date <- uw_date + random(15 jours, 60 jours)
#activity_end_date <- activity_start_date + random(0,1,2,3)
#activity_price <- c(300,1000,300,100,6000 et 25000,10000 et 50000,30,600000)
#insurance_price <- activity_price*orakle_price_ratio
#orakle_price_ratio <- random(6%,20%)


# Paramètres pour la moyenne de 50 et variance de 5
mu1 <- 20
sigma2_1 <- 10
p1 <- mu1 / (mu1 + sigma2_1)
r1 <- mu1 * p1 / (1 - p1)

# Paramètres pour la moyenne de 200 et variance de 20
mu2 <- 50
sigma2_2 <- 20
p2 <- mu2 / (mu2 + sigma2_2)
r2 <- mu2 * p2 / (1 - p2)

# Paramètres pour la moyenne de 1000 et variance de 100
mu3 <- 200
sigma2_3 <- 50
p3 <- mu3 / (mu3 + sigma2_3)
r3 <- mu3 * p3 / (1 - p3)

# Paramètres de la distribution binomiale négative
r <- c(r1,r2,r3)
p <- c(p1,p2,p3)

condition_date <- as.Date("2022-01-01")

set.seed(123)

# Initialize empty lists to store vectors
list_compagny_name <- list()
list_departement <- list()
list_category_name <- list()
list_trigger_cumulated <- list()
list_weather_guarantee_type <- list()
list_uw_date <- list()
list_random_1 <- list()
list_random_2 <- list()

for(date in uw_date){
  Date = as.Date(date)
  print(Date)
  Date <- ifelse(Date >= condition_date, as.Date(paste("2021", format(Date, "%m-%d"), sep = "-")), Date)
  if(vacancesscolr::is_holiday(Date)){
    nb_sim = rnbinom(1, size = r[3], prob = p[3]) 
  } else if(!vacancesscolr::is_holiday(Date) & weekdays(as.Date(Date)) %in% c("samedi","dimanche")){
    nb_sim = rnbinom(1, size = r[2], prob = p[2])
  } else {
    nb_sim = rnbinom(1, size = r[1], prob = p[1])
  }
  
  vec_compagny_name <- sample(company_name, size = nb_sim, replace = TRUE, prob = company_name_prob)
  vec_departement <-  sample(departement, size = nb_sim, replace = TRUE, prob = departement_prob)
  vec_category_name <- sample(category,size = nb_sim, replace = TRUE, prob = category_prob)
  vec_trigger_cumulated <- sample(trigger_cumulated, size = nb_sim, replace = TRUE)
  vec_weather_guarantee_type <- sample(weather_guarantee_type, size = nb_sim, replace = TRUE, prob = prob_weather_guarantee_type)
  vec_uw_date <- rep(as.Date(date), times = nb_sim)
  vec_random_1 <- sample(seq(15,60,by = 1), size = nb_sim, replace = TRUE)
  vec_random_2 <- sample(c(0,1,2,3),size = nb_sim, replace = TRUE,prob = c(0.8,0.1,0.05,0.05))
  
  # Store vectors in lists
  list_compagny_name[[date]] <- vec_compagny_name
  list_departement[[date]] <- vec_departement
  list_category_name[[date]] <- vec_category_name
  list_trigger_cumulated[[date]] <- vec_trigger_cumulated
  list_weather_guarantee_type[[date]] <- vec_weather_guarantee_type
  list_uw_date[[date]] <- vec_uw_date
  list_random_1[[date]] <- vec_random_1
  list_random_2[[date]] <- vec_random_2
}


price_activity_func <- function(category,numb_days){
  if(category == "Outdoor escape game"){
    return(300*numb_days)
  } else if(category == "Boat rental - cruise"){
    return(1000*numb_days)
  } else if(category == "Tourism/travel accommodation"){
    return(300*numb_days)
  } else if(category == "Cycling holidays"){
    return(100*numb_days)
  } else if(category == "Boat rental - event"){
    return(c(6000,10000,17000,25000)[numb_days])
  } else if(category == "Fireworks/drone shows"){
    return(c(10000,20000,30000,50000)[numb_days])
  } else if(category == "Amusement park"){
    return(30*numb_days)
  } else {
    return(600000)
  }
}

# Create the data frame
df_underwriting <- data.frame(
  uw_date = unlist(lapply(list_uw_date, rep, 1)),
  company_name = unlist(list_compagny_name),
  departement = unlist(list_departement),
  category = unlist(list_category_name),
  weather_guarantee_type = unlist(list_weather_guarantee_type),
  trigger_type = unlist(list_trigger_cumulated),
  Random_1 = unlist(list_random_1),
  Random_2 = unlist(list_random_2)
) %>% 
  mutate(
    policy_id = row_number(),
    uw_date = as.Date(uw_date),
         distribution_chanel = if_else(company_name == "Orakle weather","B2C/B2B","B2B2C"),
         activity_start_date = uw_date+lubridate::days(Random_1),
         threshold_trigger = if_else(Random_2 == 0,"day","stay")) %>% 
  rowwise() %>% 
  mutate(
    activity_price = price_activity_func(category = category,
                                         numb_days = 1 + Random_2),
    orakle_price_ratio = runif(1, min = 0.06, max = 0.2) 
  ) %>% 
  select(-Random_1,-Random_2)


#df_underwriting$weather_guarantee_type <- sample(weather_guarantee_type, size = nrow(df_underwriting), replace = TRUE, prob = prob_weather_guarantee_type)
  
saveRDS(df_underwriting,"df_underwriting.rds")


## base de sinistre
# Define the function
sample_data_by_group <- function(df, group_col, frac) {
  sampled_df <- df %>%
    group_by(across(all_of(group_col))) %>%
    sample_frac(frac) %>%
    ungroup()
  
  return(sampled_df)
}

df_claims <- sample_data_by_group(df_underwriting, c("company_name","departement","category"), 0.13)

df_claims["activity_maintained"] <- sample(c(0,1),size = nrow(df_claims), replace = TRUE,prob = c(0.4,0.6))
df_claims <- df_claims %>% 
                transmute(
                  claim_id = row_number(),
                  policy_id,
                  date_of_claim = activity_start_date,
                  activity_maintained,
                  amount_of_claims = if_else(activity_maintained == 1,0.5*activity_price,activity_price)
                  )

#df_claims <- df_claims %>% 
#           left_join(df_underwriting %>% select(policy_id,activity_start_date),by = "policy_id")
# df_claims <- df_claims %>% 
#     rename(date_of_claim = activity_start_date)
# 
df_claims <- df_claims %>% 
              filter(date_of_claim <= as.Date("2024-07-04"))
saveRDS(df_claims,"df_claims.rds")