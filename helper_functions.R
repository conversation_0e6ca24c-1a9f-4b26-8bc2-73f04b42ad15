library(lubridate)
library(tidyr)
library(dplyr)
library(purrr)
library(purrr)
# Function to get latitude and longitude from an address using Nominatim API
get_lat_lon <- function(address) {
  base_url <- "https://nominatim.openstreetmap.org/search"
  query <- list(q = address, format = "json", limit = 1)
  
  response <- httr::GET(base_url, query = query)
  content <- httr::content(response, as = "parsed", type = "application/json")
  
  if (length(content) > 0) {
    lat <- content[[1]]$lat
    lon <- content[[1]]$lon
    return(paste0(lon,",",lat))
  } else {
    return('')
  }
}


connect_to_db_Orakle_Weather <- function() {
  
  DBI::dbConnect(
    drv = RPostgres::Postgres(),
    dbname = Sys.getenv("DB_NAME_OW"), 
    host = Sys.getenv("DB_HOST_OW"), 
    port = Sys.getenv("DB_PORT_OW"),  
    user = Sys.getenv("DB_USER_OW"),  
    password = Sys.getenv("DB_PWD_OW")  
  )
  
}

create_dm <- function(use_Fake_Data) {
  
  # Data corresponding table region, department of french
  reg_corres <- readxl::read_xlsx('www/tab_region_corr.xlsx')
  dep_corres <- readxl::read_xlsx('www/tab_departement_corr.xlsx') %>%
    left_join(reg_corres %>% select('hc-a2', 'code_insee_reg'), 
              by = c('code_insee_reg'),
              keep = FALSE
    ) 
  
  
  # Establish connection to PostgreSQL database
  if(use_Fake_Data){
    df_claims <- readRDS("www/df_claims.rds")
    df_underwriting <- readRDS("www/df_underwriting.rds")
    
      
    data_df <- list(
      df_claims = df_claims,
      df_underwriting = df_underwriting,
      reg_corres = reg_corres,
      dep_corres = dep_corres,
      frgeoson_reg = frgeoson_reg,
      frgeoson_dep = frgeoson_dep
    )
    
    return(data_df)
  }
  
  #con <- connect_to_db()
}

read_data_from_table <- function(connection, table_name, column_names) {
  
  DBI::dbGetQuery(
    conn = connection,
    statement = glue::glue_sql(
      "SELECT {`column_names`*} FROM {`table_name`}",
      .con = connection
    )
  )
  
}

format_euro <- function(value) {
  formatted_value <- paste("\u20ac", format(value, big.mark = ",", decimal.mark = ".", nsmall = 2), sep = "")
  return(formatted_value)
}

# Function to group by variable column name and summarize
group_and_summarize <- function(data, group_var, value_var, summary_func = "sum", new_name = "date") {
  # Convert group_var and value_var to symbols and then to quosures
  group_var <- enquo(group_var)
  value_var <- enquo(value_var)
  new_name_sym <- sym(new_name)
  
  # Rename the grouping variable to new_name
  data <- data %>% rename(!!new_name_sym := !!group_var)
  
  # Summarize based on the summary_func argument
  if (summary_func == "mean") {
    data %>%
      group_by(!!new_name_sym) %>%
      summarize(summary_value = mean(!!value_var), .groups = 'drop')
  } else if (summary_func == "sum") {
    data %>%
      group_by(!!new_name_sym) %>%
      summarize(summary_value = sum(!!value_var), .groups = 'drop')
  } else {
    stop("Invalid summary function. Please use 'mean' or 'sum'.")
  }
}

### telechargement du fichier map
download_map <- function(url,quiet = FALSE){
  tmpfile <- tempfile(fileext = ".js")
  download.file(url, tmpfile, quiet = quiet)
  mapdata <- readLines(tmpfile, warn = FALSE, encoding = "UTF-8")
  mapdata[1] <- gsub(".* = ", "", mapdata[1])
  mapdata <- paste(mapdata, collapse = "\n")
  mapdata <- jsonlite::fromJSON(mapdata, simplifyVector = FALSE)
  return(mapdata)
}

url_dep = "https://code.highcharts.com/mapdata/countries/fr/fr-all-all.geo.json"
url_reg = "https://code.highcharts.com/mapdata/countries/fr/fr-all.geo.json"

frgeoson_reg = download_map(url_reg)
frgeoson_dep = download_map(url_dep)

# write_json(frgeoson_dep, path = "frgeoson_dep.json", pretty = TRUE)
