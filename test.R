bordereau <- donnees_completes %>% 
  filter(triggered == TRUE & (company_name == "<PERSON><PERSON><PERSON>" | company_name ==  "Flower") )

bordereau <- bordereau %>%
  mutate(
    Identification_Client = paste0(first_name, last_name),
    Date_de_souscription = as.Date(uw_date,format = "%d/%m/%Y"),
    Date_de_survenance = trip_day,
    Date_de_déclaration = trip_day,
    Statut_du_dossier = "Ouvert",
    ID_Distributeur = partner_id,
    Nom_Distributeur = company_name,
    Activité = activity_category,
    Nom_camping = activity_name,
    Provisions_techniques  = sinistre,
    Référence_Client = policy_code
  )

excel <- readxl::read_xlsx("ORAKLE_Bordereau Sinistre_06_2025_R.xlsx")
excel <- excel %>% select(ID_Distributeur,Nom_Distributeur,Activité,Identification_Client,Nom_camping, Référence_Client,
                          Date_de_souscription,Date_de_survenance,Date_de_déclaration,Statut_du_dossier,Provisions_techniques)

ok <- bordereau %>% select(ID_Distributeur,Nom_Distributeur,Activité,Identification_Client,Nom_camping, Référence_Client,
                           Date_de_souscription,Date_de_survenance,Date_de_déclaration,Statut_du_dossier,Provisions_techniques)

tot <- rbind(excel,ok)

saveRDS(donnees_completes,"donnees_serveurs_11_juillet.RDS")
