CONS <- use("constants/constants.R")

## load functions
source("helper_functions.R")


ui <- bs4Dash::dashboardPage(
    
    title = "Orakle Weather",
    
    help = NULL,
    
    header = bs4Dash::dashboardHeader(
      title = bs4Dash::dashboardBrand(
        title = "Profitability Dashboard",
        color = "primary",
        href = "https://orakleweather.com/",
        image = "orakleweather_logo.jpg"
      ),
      controlbarIcon = shiny::icon("filter")
    ),
    
    sidebar = bs4Dash::dashboardSidebar(
      
      bs4Dash::sidebarMenu(
        id = "left_sidebar_menu",
        bs4Dash::menuItem(
          text = "Welcome",
          tabName = "tab_welcome",
          icon = shiny::icon("home")
        ),
        bs4Dash::menuItem(
          text = "Premium",
          tabName = "tab_premium",
          icon = shiny::icon("euro-sign")
        ),
        bs4Dash::menuItem(
          text = "Exposure",
          tabName = "tab_exposure",
          icon = shiny::icon("exclamation-triangle")
        ),
        bs4Dash::menuItem(
          text = "Claims",
          tabName = "tab_claims",
          icon = shiny::icon("file-invoice")
        ),
        
        ### Arnaud
        bs4Dash::menuItem(
          text = "Camping Checks",
          tabName = "tab_camping",
          icon = shiny::icon("tent")
        )
        ###
      )
      
    ),
    
    controlbar = bs4Dash::bs4DashControlbar(
      shiny::fluidRow(
        shiny::column(
          width = 12,
          
          # choice of the date type
          shinyWidgets::prettyRadioButtons(
            inputId = "date_type_id",
            label = "Date type:", 
            choices = c("Underwriting", "Occurence"),
            icon = icon("check"), 
            inline = TRUE, 
            bigger = TRUE,
            status = "info",
            animation = "jelly"
          ),
          
          # Date range for visualization
          shiny::dateRangeInput(
            inputId = "date_range_id",
            label = "Date range:",
            width = "460px",
            start = NULL,
            end = lubridate::today(),
            min = NULL,
            max = lubridate::today()
          ),
          
          # Policiy ID filter
          shinyWidgets::pickerInput(
            inputId = "policy_id",
            label = "Policy ID:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Partners filter
          shinyWidgets::pickerInput(
            inputId = "partner_id",
            label = "Parner name:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Activity filter
          shinyWidgets::pickerInput(
            inputId = "activity_id",
            label = "Activity:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Distribution channel filter
          shinyWidgets::pickerInput(
            inputId = "distribution_channel_id",
            label = "Distribution channel:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Type of threshold filter
          shinyWidgets::pickerInput(
            inputId = "type_of_threshold_id",
            label = "Type of threshold:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Type of trigger filter
          shinyWidgets::pickerInput(
            inputId = "type_of_trigger_id",
            label = "Type of trigger:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Type of peril filter
          shinyWidgets::pickerInput(
            inputId = "type_of_peril_id",
            label = "Type of peril:",
            width = "400px",
            choices = NULL,
            selected = NULL,
            multiple = TRUE,
            options = list(
              `actions-box` = TRUE,
              `selected-text-format` = 'count > 2'
            )
          ),
          
          # Action button to apply filters
          div(
          shiny::actionButton(
            inputId = "apply_filters_id",
            label = "Apply",
            icon = icon("play"),
            class = "bg-primary"
          ),
          shiny::actionButton(
            inputId = "reset", 
            label = "Reset", 
            icon = icon("sync"),
            class = "bg-primary"
          )
        )
        )
        ),
      id = "control_bar",
      width = 500,
      skin = "dark",
      type = "tabs",
      collapsed = TRUE
    ),
    
    
    
    body = bs4Dash::dashboardBody(
      #use_theme(CONS$bs4DashTheme),
      waiter::useWaiter(), # Include waiter dependencies
      bs4Dash::tabItems(
        bs4Dash::tabItem(
          tabName = "tab_welcome",
          bs4Dash::bs4Jumbotron(
            title = "Welcome to the Shiny Profitability Dashboard",
            lead = "This is a Shiny application that aims to follow and monitor profibility of Orakle Weather portfolio",
            status = "primary",
            btnName = "Go to premium tab"
            ),
          fluidRow(
          userBox(
            title = userDescription(
              title = "Application Developer and maintainer",
              subtitle = "Youssouf BANCE",
              type = 2,
              image = "téléchargement.jpg",
            ),
            status = "primary",
            closable = TRUE,
            "Actuary - Pricing and Porfolio monitoring",
            footer = "Associate actuary IA"
          ),
            
            userBox(
              title = userDescription(
                title = "Application Developer and maintainer",
                subtitle = "Arnaud DURAND",
                type = 2,
                image = "arnaud_durand.jpg",
              ),
              status = "primary",
              closable = TRUE,
              "Actuary",
              footer = NULL
              ),
            
          userBox(
            title = userDescription(
              title = "Key contributor",
              subtitle = "Stella Jovet",
              type = 2,
              image = "stella_jovet.jpg",
            ),
            status = "primary",
            closable = TRUE,
            "Co-founder of orakle weather",
            footer = "Fully Qualified Actuary IA"
          )
          )
          ),
        bs4Dash::tabItem(
          tabName = "tab_premium",
          shiny::fluidRow(
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "total_premium_id"),
              subtitle = "Total amount of premium",
              icon = shiny::icon("user", class = "fa-solid"),
              color = "success",
              width = 4
            ),
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId ="number_of_policies_id"),
              subtitle = "Number of policies",
              color = "success",
              icon = shiny::icon("stethoscope"),
              width = 4
            ),
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "stats_prem_id"),
              subtitle = "Statistics on premiums",
              color = "success",
              icon = shiny::icon("stethoscope"),
              width = 4
            )
            
          )
        ),
        
        bs4Dash::tabItem(
          tabName = "tab_exposure",
          shiny::fluidRow(
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "total_exposure_id"),
              subtitle = "Total amount of exposure",
              icon = shiny::icon("user", class = "fa-solid"),
              color = "warning",
              width = 4
            )
            
          )
        ),
        
        bs4Dash::tabItem(
          tabName = "tab_claims",
          shiny::fluidRow(
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "total_claims_id"),
              subtitle = "Total amount of claims",
              icon = shiny::icon("truck-medical", class = "fa-solid"),
              color = "danger",
              width = 4
            ),
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "nb_claims_id"),
              subtitle = "Number of claims",
              color = "danger",
              icon = shiny::icon("book"),
              width = 4
            ),
            
            bs4Dash::bs4ValueBox(
              value = shiny::textOutput(outputId = "means_on_claims_id"),
              subtitle = "Means of claims amount",
              color = "danger",
              icon = shiny::icon("calculator"),
              width = 4
            )
          ),
          shiny::fluidRow(
            
            bs4Dash::bs4InfoBox(
              value = shiny::textOutput(outputId = "frequency_claims_id"),
              title = "Claim frequency",
              icon = shiny::icon("radio"),
              color = "danger",
              width = 4
            ),
            
            bs4Dash::bs4InfoBox(
              value = shiny::textOutput(outputId = "LR_id"),
              title = "Loss Ratio (LR)",
              color = "danger",
              icon = shiny::icon("sack-dollar"),
              width = 4
            ),
            
            bs4Dash::bs4InfoBox(
              value = shiny::textOutput(outputId = "rate_cancelled_id"),
              title = "Cancellation rate",
              color = "danger",
              icon = shiny::icon("percent"),
              width = 4
            )
          ),
          
          shiny::hr(),
          
          shiny::fluidRow(
            
            shiny::column(
              width = 12,
              
              bs4Dash::tabsetPanel(
                type = "pills",
                
                # Total  ----
                shiny::tabPanel(
                  title = "Total",
                  fluidRow(
                  bs4Card(
                    title = "Claim amount per day",
                    maximizable = TRUE,
                    width = 6,
                    status = "warning",
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    label = bs4CardLabel(
                      text = 1,
                      status = "danger",
                      tooltip = "Hello!"
                    ),
                    highcharter::highchartOutput("claims_per_days_chart")
                  ),
                  bs4Card(
                    title = uiOutput("dynamic_title_claims_1"),
                    maximizable = TRUE,
                    width = 6,
                    status = "warning",
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    label = bs4CardLabel(
                      text = 1,
                      status = "danger",
                      tooltip = "Hello!"
                    ),
                    sidebar = bs4CardSidebar(
                      id = "my_sidebar",
                      width = 25,
                      selectInput(
                        inputId = "Variable_choice_Id_1",
                        label = "Select variable:",
                        choices = c("Claims amount" = "Claims_amts",
                                    "Number of claims" = "Claim_count",
                                    "Frequency" = "Frequency",
                                    "Cancellation rate" = "Cancellation_rate",
                                    "Results" = "Results",
                                    "Loss Ratio (LR)" = "LR")
                       ),
                      shinyWidgets::switchInput(
                        inputId = "cumulated_choice_Id1",
                        label = "Cumulated", 
                        labelWidth = "80px"
                      )
                    ),
                    plotly::plotlyOutput("claims_per_month_year")
                  )
                  ),
                  fluidRow(
                    bs4Card(
                      title = uiOutput("dynamic_title_claims_2"),
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      label = bs4CardLabel(
                        text = 1,
                        status = "danger",
                        tooltip = "Hello!"
                      ),
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar2",
                        width = 25,
                        selectInput(
                          inputId = "Variable_choice_Id_2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR")
                        ),
                        shinyWidgets::switchInput(
                          inputId = "cumulated_choice_Id2",
                          label = "Cumulated", 
                          labelWidth = "80px"
                        )
                      ),
                      plotly::plotlyOutput("claims_per_weekof_year")
                    ),
                    
                    bs4Card(
                      title = "Top 10 claims by amount",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      highcharter::highchartOutput("top_ten_claims")
                    )
                    
                  )
                  ),
                
                # Segmentation by partners name  ----
                shiny::tabPanel(
                  title = "Partners",
                  fluidRow(
                    bs4Card(
                      title = "Partners distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_partners",
                        width = 25,
                        selectInput(
                          inputId = "partners_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      highcharter::highchartOutput("Partners_distribution")
                    ),
                    bs4Card(
                      title = "Cancellation rate by partner",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      highcharter::highchartOutput("claims_amount_by_partners")
                    )
                  ),
                  fluidRow(
                    bs4Card(
                      title = "Claim amount per month over year by partners",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      echarts4r::echarts4rOutput("barChartRace_claim_amts")
                    ),
                    bs4Card(
                      title = "Performance indicator by partners",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      DT::DTOutput("Performance_indicator_by_partners")
                    )
                  )
                ),
                
                # Segmentation by distribution channel  ----
                shiny::tabPanel(
                  title = "Distribution channel",
                  fluidRow(
                    bs4Card(
                      title = "Distribution channel distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar2",
                        width = 25,
                        selectInput(
                          inputId = "distribution_channel_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      amChartsOutput(outputId = "Distribution_channel_dist")
                    ),
                    bs4Card(
                      title = uiOutput("dynamic_title_channel_dist_1"),
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar3",
                        width = 25,
                        selectInput(
                          inputId = "distribution_channel_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        ),
                        shinyWidgets::switchInput(
                          inputId = "stacked_choice_Id1",
                          label = "Stacked", 
                          labelWidth = "80px"
                        )
                      ),
                      plotly::plotlyOutput("distribution_chanel_by_month")
                    )
                  )
                ),
                
                
                # Segmentation by activity ----
                shiny::tabPanel(
                  title = "Activity",
                  fluidRow(
                    bs4Card(
                      title = "Activity type distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_activity",
                        width = 25,
                        selectInput(
                          inputId = "activity_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      amChartsOutput(outputId = "Activity_dist")
                    ),
                    bs4Card(
                      title = "Claim amount per activity",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      highcharter::highchartOutput("claims_amount_by_activity")
                    )
                  ),
                  fluidRow(
                    bs4Card(
                      title = "Claim amount per month over year by activity",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      echarts4r::echarts4rOutput("barChartRace_claim_activity")
                    ),
                    bs4Card(
                      title = "Performance indicator by activity",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      DT::DTOutput("Performance_indicator_by_activity")
                    )
                  )
                ),
                
                # Segmentation by peril ----
                shiny::tabPanel(
                  title = "Peril type",
                  fluidRow(
                    bs4Card(
                      title = "Peril type distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_peril",
                        width = 25,
                        selectInput(
                          inputId = "peril_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      amChartsOutput(outputId = "peril_dist")
                    ),
                    bs4Card(
                      title = uiOutput("dynamic_title_channel_peril_1"),
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_peril",
                        width = 25,
                        selectInput(
                          inputId = "peril_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        ),
                        shinyWidgets::switchInput(
                          inputId = "stacked_choice_Id1_peril",
                          label = "Stacked", 
                          labelWidth = "80px"
                        )
                      ),
                      plotly::plotlyOutput("peril_by_month")
                    )
                  )
                ),
                
                # Segmentation by threshold type  ----
                shiny::tabPanel(
                  title = "Threshold type",
                  fluidRow(
                    bs4Card(
                      title = "Threshold type distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_Threshold",
                        width = 25,
                        selectInput(
                          inputId = "Threshold_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      amChartsOutput(outputId = "Threshold_dist")
                    ),
                    bs4Card(
                      title = uiOutput("dynamic_title_channel_Threshold_1"),
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_Threshold",
                        width = 25,
                        selectInput(
                          inputId = "Threshold_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        ),
                        shinyWidgets::switchInput(
                          inputId = "stacked_choice_Id1_Threshold",
                          label = "Stacked", 
                          labelWidth = "80px"
                        )
                      ),
                      plotly::plotlyOutput("Threshold_by_month")
                    )
                  )
                ),
                # Segmentation by trigger type  ----
                shiny::tabPanel(
                  title = "Trigger type",
                  fluidRow(
                    bs4Card(
                      title = "Trigger type distribution",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_Trigger",
                        width = 25,
                        selectInput(
                          inputId = "Trigger_var_id",
                          label = "Select variable:",
                          choices = c("Claims amount","Number of claims")
                        )
                      ),
                      amChartsOutput(outputId = "Trigger_dist")
                    ),
                    bs4Card(
                      title = uiOutput("dynamic_title_channel_trigger_1"),
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_trigger",
                        width = 25,
                        selectInput(
                          inputId = "trigger_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        ),
                        shinyWidgets::switchInput(
                          inputId = "stacked_choice_Id1_trigger",
                          label = "Stacked", 
                          labelWidth = "80px"
                        )
                      ),
                      plotly::plotlyOutput("trigger_by_month")
                    )
                  )
                ),
                # Segmentation by Month/Year  ----
                shiny::tabPanel(
                  title = "Year/Month",
                  fluidRow(
                    bs4Card(
                      title = "(Variable) by month/Year drilldown",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_my1",
                        width = 25,
                        selectInput(
                          inputId = "monthYear_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        )
                      ),
                      highcharter::highchartOutput("claims_amount_by_month_Year_ID")
                    )
                  )
                ),
                # Segmentation by Geographical  ----
                shiny::tabPanel(
                  title = "Geographical",
                  fluidRow(
                    
                    bs4Card(
                      title = "Variable by department",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_region",
                        width = 25,
                        selectInput(
                          inputId = "departement_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        )),
                      highcharter::highchartOutput("variable_by_departement")
                    ),
                    bs4Card(
                      title = "Variable by region",
                      maximizable = TRUE,
                      width = 6,
                      status = "warning",
                      solidHeader = FALSE,
                      collapsible = TRUE,
                      sidebar = bs4CardSidebar(
                        id = "my_sidebar_region",
                        width = 25,
                        selectInput(
                          inputId = "region_var2",
                          label = "Select variable:",
                          choices = c("Claims amount" = "Claims_amts",
                                      "Number of claims" = "Claim_count",
                                      "Frequency" = "Frequency",
                                      "Cancellation rate" = "Cancellation_rate",
                                      "Results" = "Results",
                                      "Loss Ratio (LR)" = "LR",
                                      "Destruction Rate (DR)" = "Destruction_rate")
                        )),
                      highcharter::highchartOutput("variable_by_region")
                    )
                  )
                )
  
              )
            )
          )
        ),
        
        
##################################################################################################################################################################
                                                                    ###Arnaud
        
        bs4Dash::tabItem(
          tabName = "tab_camping",
          shiny::fluidRow(

            # Choix du type de données utilisées
            shinyWidgets::prettyRadioButtons(
              inputId = "data_type_id",
              label = "Type de données à utiliser:",
              choices = c("Local", "Serveur"),
              icon = icon("check"),
              inline = TRUE,
              bigger = TRUE
            ),

            column(6,tags$div(style = "padding: 10px 0;")),
            
            # Choix des dates observées
            shiny::dateRangeInput(
              inputId = "date_range_camping_id",
              label = "Date range:",
              width = "1000px",
              start = NULL,
              end = NULL,
              min = NULL,
              max = NULL,
              format = "dd-mm-yyyy"
            ),
            
            # Choix du partenaire obervé
            shinyWidgets::pickerInput(
              inputId = "camping_partenaire_id",
              label = "Liste partenaires:",
              width = "1000px",
              choices = c("Oléla","Flower"),
              selected = "Oléla",
              multiple = FALSE,
              options = list(
                `actions-box` = TRUE,
                `selected-text-format` = 'count > 2'
              )
            )
          ),
          

          shiny::column(
            width = 12,
              
            bs4Dash::tabsetPanel(
              type = "pills",
              
              shiny::tabPanel(
                title = "Sommes assurées",
                fluidRow(
                    
                  box(
                    title = "Nombre de clients",
                    width = 4,
                    shinycssloaders::withSpinner(textOutput("Nombre_de_clients"), type = 4, color = "#0dc5c1")
                  ),

                  box(
                    title = "Nombre de journées couvertes",
                    width = 4,
                    shinycssloaders::withSpinner(textOutput("Nombre_journees_couvertes"), type = 4, color = "#0dc5c1")
                  ),

                  box(
                    title = "Somme assurée",
                    width = 4,
                    shinycssloaders::withSpinner(textOutput("Somme_assuree"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Graphique du nombre de client couverts par jour",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(highcharter::highchartOutput("Graph_clients_par_jour"), type = 4, color = "#0dc5c1")
                  ),


                  bs4Card(
                    title = "Graphique de la somme assurée par jour",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(highcharter::highchartOutput("Graph_somme_assuree_par_jour"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Liste des clients de la période choisie",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(DTOutput("liste_clients"), type = 4, color = "#0dc5c1")
                  )
                )
              ),
              
              shiny::tabPanel(
                title = "Filtre par numéro de réservation",
                fluidRow(
                  
                  bs4Card(
                    title = "Données du client",
                    width = 3,
                    maximizable = TRUE,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    numericInput("num_resa","Numéro de réservation", value = NULL),
                    shinycssloaders::withSpinner(uiOutput("donnees_client"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Tableau des précipitations du client en mm par heure",
                    maximizable = TRUE,
                    width = 9,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("tableau_precipitations"), type = 4, color = "#0dc5c1")
                  ),


                  bs4Card(
                    title = "Infos globales du numéro de contrat choisis",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("liste_clients_precipitations"), type = 4, color = "#0dc5c1")
                  )
                )
              ),
              
              shiny::tabPanel(
                title = "Filtre par policy code",
                fluidRow(

                  bs4Card(
                    title = "Données du client",
                    width = 3,
                    maximizable = TRUE,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    numericInput("policy_code","Policy Code", value = NULL),
                    shinycssloaders::withSpinner(uiOutput("donnees_client_pc"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Tableau des précipitations du client en mm par heure",
                    maximizable = TRUE,
                    width = 9,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("tableau_precipitations_pc"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Infos globales du policy code choisis",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("liste_clients_precipitations_pc"), type = 4, color = "#0dc5c1")
                  )
                )
              ),
              
              shiny::tabPanel(
                title = "Filtre par camping",
                fluidRow(
                  
                  bs4Card(
                    title = "Données du camping",
                    width = 3,
                    selectizeInput("nom_camping",
                                   label = "Nom du camping",
                                   choices = NULL,
                                   options = list(
                                     placeholder = "Commencez à taper...",
                                     maxOptions = 1000,
                                     create = FALSE
                                   )),
                    shinycssloaders::withSpinner(uiOutput("infos_camping"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Tableau des précipitations du camping en mm par heure",
                    maximizable = TRUE,
                    width = 9,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("tableau_precipitations_camping"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Infos globales du camping choisis",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("liste_camping_precipitations"), type = 4, color = "#0dc5c1")
                  )
                )
              ),

              shiny::tabPanel(
                title = "Recap global",
                fluidRow(

                  bs4Card(
                    title = "Résumer de la couverture sur les dates choisies",
                    width = 6,
                    maximizable = TRUE,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("couverture"), type = 4, color = "#0dc5c1"),
                    shinycssloaders::withSpinner(uiOutput("couverture_unique"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Données globales par camping",
                    width = 12,
                    maximizable = TRUE,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("globales_campings"), type = 4, color = "#0dc5c1")
                  ),

                  bs4Card(
                    title = "Infos clients et précipitations sur la période choisie",
                    maximizable = TRUE,
                    width = 12,
                    solidHeader = FALSE,
                    collapsible = TRUE,
                    shinycssloaders::withSpinner(uiOutput("liste_clients_precipitations_global"), type = 4, color = "#0dc5c1")
                  )
                )
              ),
              
                  shiny::tabPanel(
                    title = "Recap par jour",
                    fluidRow(
                      
                      bs4Card(
                        title = "Date choisie",
                        width = 2,
                        shinycssloaders::withSpinner(uiOutput("date_debut"), type = 4, color = "#0dc5c1")
                      ),

                      bs4Card(
                        title = "Résumer de la couverture journalière",
                        width = 4,
                        maximizable = TRUE,
                        solidHeader = FALSE,
                        collapsible = TRUE,
                        shinycssloaders::withSpinner(uiOutput("couverture_journaliere"), type = 4, color = "#0dc5c1")
                      ),

                      bs4Card(
                        title = "Carte des campings assuré le jour choisis",
                        width = 12,
                        shinycssloaders::withSpinner(leafletOutput("franceMap", height = "600px"), type = 4, color = "#0dc5c1"),
                        shinycssloaders::withSpinner(uiOutput("legende_carte"), type = 4, color = "#0dc5c1")
                      ),


                      bs4Card(
                        title = "Données globales par camping",
                        width = 12,
                        maximizable = TRUE,
                        solidHeader = FALSE,
                        collapsible = TRUE,
                        shinycssloaders::withSpinner(uiOutput("globales_campings_journalier"), type = 4, color = "#0dc5c1")
                      ),

                      bs4Card(
                        title = "Infos clients et précipitations sur la période choisie",
                        maximizable = TRUE,
                        width = 12,
                        solidHeader = FALSE,
                        collapsible = TRUE,
                        shinycssloaders::withSpinner(uiOutput("liste_clients_precipitations_global_journalier"), type = 4, color = "#0dc5c1")
                      )
                    )
                  )
                )
              )
            )
          )
        )
      )
