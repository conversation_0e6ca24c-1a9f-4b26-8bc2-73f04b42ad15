CONS <- use("constants/constants.R")

## Raj<PERSON>er tous les jours 
function(input,output,session){
  
  # Create dm object. This is run once per session
  dm <- create_dm(TRUE)
  print(dm$df_underwriting)
  # print(dm$df_underwriting$uw_date)
  
  if (nrow(dm$df_underwriting) > 0) {
    
    ## Update the button
    # Update the values in the filters given the {dm} data
    shiny::observeEvent(input$reset,{
      
      shiny::req(dm)
      
      shiny::updateDateRangeInput(
        session = session,
        inputId = "date_range_id",
        start = if(input$date_type_id == "Underwriting"){min(dm$df_underwriting$uw_date)}
                  else {min(dm$df_claims$date_of_claim)},
        min = if(input$date_type_id == "Underwriting"){min(dm$df_underwriting$uw_date)}
        else {min(dm$df_claims$date_of_claim)},
        end = if(input$date_type_id == "Underwriting"){max(dm$df_underwriting$uw_date)}
        else {max(dm$df_claims$date_of_claim)}
      )
      
      # shinyWidgets::updatePickerInput(
      #   session = session,
      #   inputId = "policy_id",
      #   choices = unique(dm$df_underwriting$policy_id) %>%  sort(),
      #   selected = c(1,2,3)
      # )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "partner_id",
        choices = unique(dm$df_underwriting$company_name) %>%  sort(),
        selected = unique(dm$df_underwriting$company_name) %>%  sort()
      )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "activity_id",
        choices = unique(dm$df_underwriting$category) %>%  sort(),
        selected = unique(dm$df_underwriting$category) %>%  sort()
      )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "distribution_channel_id",
        choices = unique(dm$df_underwriting$distribution_chanel) %>%  sort(),
        selected = unique(dm$df_underwriting$distribution_chanel) %>%  sort()
      )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "type_of_threshold_id",
        choices = unique(dm$df_underwriting$threshold_trigger) %>%  sort(),
        selected = unique(dm$df_underwriting$threshold_trigger) %>%  sort()
      )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "type_of_trigger_id",
        choices = unique(dm$df_underwriting$trigger_type) %>%  sort(),
        selected = unique(dm$df_underwriting$trigger_type) %>%  sort()
      )
      
      shinyWidgets::updatePickerInput(
        session = session,
        inputId = "type_of_peril_id",
        choices = unique(dm$df_underwriting$weather_guarantee_type) %>%  sort(),
        selected = unique(dm$df_underwriting$weather_guarantee_type) %>%  sort()
      )
    },ignoreNULL = FALSE)
    
  } else {
    
    # Hide filtering and navigation elements when there is no data available
    
    # List ids of elements to hide
    c(
      "tab_premium",
      "tab_exposure",
      "tab_claims",
      "controlbar-toggle"
    ) |>
      # Hide elements
      purrr::map(function(id) shinyjs::hide(id))
  }
  
  df_claims_underwriting <- shiny::eventReactive(input$apply_filters_id, {
    # We apply filtered to df_underwriting
    #print("bbczdhsbq")
    df_claims <- dm$df_claims
    df_underwriting <- dm$df_underwriting
    print(df_claims)
    print(df_underwriting)
    
    setDT(df_underwriting)
    setDT(df_claims)
    
    if(input$date_type_id == "Underwriting"){
      df_underwriting <- dm$df_underwriting %>% 
        dplyr::filter(
          dplyr::between(
            x = uw_date,
            left = input$date_range_id[1],
            right = input$date_range_id[2]
          )
        )
    } else {
      df_underwriting <- dm$df_underwriting %>% 
        dplyr::filter(
          dplyr::between(
            x = activity_start_date,
            left = input$date_range_id[1],
            right = input$date_range_id[2]
          )
        )
    }
    
    ## filtered for others variables
    df_underwriting <- df_underwriting %>% 
      dplyr::filter(
        company_name %in% input$partner_id,
        category %in% input$activity_id,
        trigger_type %in% input$type_of_trigger_id,
        distribution_chanel %in% input$distribution_channel_id,
        threshold_trigger %in% input$type_of_threshold_id,
        weather_guarantee_type %in% input$type_of_peril_id
      )
    
    df_merge <- merge(df_underwriting, df_claims, by = "policy_id", all.x = TRUE)
    
    # Replace NA values in amount_of_claims with 0 and create the frequency column
    df_merge[, `:=`(amount_of_claims = ifelse(is.na(amount_of_claims), 0, amount_of_claims),
                    frequency = ifelse(is.na(amount_of_claims), 0, 1))]
    df_merge
    },ignoreNULL = FALSE)
  
  
  #### Claims tab
  # Total amount of claims
  total_claims_id <- shiny::reactive({
    req(df_claims_underwriting())
    df_claims_underwriting()[,sum(amount_of_claims)]
  })
  
  # Render total amount of claims box value
  output$total_claims_id <- shiny::renderText({
    total_claims_id() %>% 
      format_euro()
  })
  
  # Number of claims
  nb_claims_id <- shiny::reactive({
    req(df_claims_underwriting())
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      nrow() })
  
  # Render Number of claims box value
  output$nb_claims_id <- shiny::renderText({
    nb_claims_id() %>% 
      prettyNum(big.mark = ",")
  })
  
  # Means of claims
  means_on_claims_id <- shiny::reactive({
    req(df_claims_underwriting())
    df_claims_underwriting() %>%
      filter(amount_of_claims > 0) %>% 
      dplyr::summarise(
        claims_means = mean(amount_of_claims)
      ) %>% 
      pull() 
  })
  
  # Render mean of claims amount box value
  output$means_on_claims_id <- shiny::renderText({
    means_on_claims_id() %>% 
      format_euro()
  })

  # Cancellation rate
  rate_cancelled_id <- shiny::reactive({
    req(df_claims_underwriting())
    df_claims_underwriting() %>% 
      filter(frequency == 1) %>% 
      summarise(
        percent_cancel = 100*mean(activity_maintained)
      ) %>% 
      pull() %>%
      round(2) %>%
      paste0("%")
  })
  
  # Render cancellation rate box value
  output$rate_cancelled_id <- shiny::renderText({ 
    rate_cancelled_id()
  })
  
  # Claims frequency
  frequency_claims_id <- shiny::reactive({
    req(df_claims_underwriting())
    
    100*df_claims_underwriting()[,mean(frequency)]
    
  })
  
  # Render claims frequency box value
  output$frequency_claims_id <-  shiny::renderText({
    frequency_claims_id() %>%
      round(2) %>%
      paste0("%")
  })
  
  # Loss ratio
  LR_id <- shiny::reactive({
    req(df_claims_underwriting())
    
    df_claims_underwriting() %>% 
      summarise(
        total_premiums = sum(activity_price*orakle_price_ratio),
        total_claims = sum(amount_of_claims)
      ) %>% 
      mutate(
        Loss_Ratio = 100 * (total_claims/total_premiums)
      ) %>% 
      pull() %>%
      round(2) %>%
      paste0("%")
  })
  
  # Render loss ratio box value
  output$LR_id <-  shiny::renderText({
    LR_id()
  })
  
  # Render plot claims amount per days
  claims_amount_daily <- shiny::reactive({
    req(df_claims_underwriting())
    
    if(isolate(input$date_type_id) == "Underwriting"){
      data = group_and_summarize(data = df_claims_underwriting(),group_var = uw_date,value_var  = amount_of_claims)
    } else {
      data = group_and_summarize(data = df_claims_underwriting(),group_var = activity_start_date,value_var  = amount_of_claims)
    }
    
    xts::xts(data$summary_value, order.by = data$date)
  })
  
  output$claims_per_days_chart <- highcharter::renderHighchart({
    hchart(claims_amount_daily(), type = "line") %>% 
      hc_rangeSelector(
        selected = 3)
  })
  
  # Render plot claims amount per month over year
  Variables_MonthYear <- shiny::reactive({
    req(df_claims_underwriting)
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(uw_date, "%Y-%m") )
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(activity_start_date, "%Y-%m") )
    }
    
    df_data = df_data %>%  
      dplyr::mutate(
        Year = as.numeric(substr(YearMonth, 1, 4)),
        Month = as.numeric(substr(YearMonth, 6, 7))
      )
    
    # Computing indicator and variables
    data <- df_data %>% 
      dplyr::group_by(Year,Month) %>% 
      dplyr::summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE)
      ) %>% 
      dplyr::arrange(Year, Month) %>%
      dplyr::group_by(Year) %>%
      dplyr::mutate(
        Cum_Claim_count = cumsum(Claim_count),
        Cum_Policy_count = cumsum(Policy_count),
        Cum_Premium = cumsum(Premium),
        Cum_Claims_amts = cumsum(Claims_amts),
        Cum_Number_maintained = cumsum(Number_maintained)
      ) %>% 
      dplyr::mutate(
        Frequency = 100*(Claim_count/Policy_count),
        Cancellation_rate = 100*(1 - Number_maintained/Claim_count),
        Results = Premium - Claims_amts,
        LR = 100*Claims_amts/Premium,
        Cum_Frequency = 100*(Cum_Claim_count/Cum_Policy_count),
        Cum_Cancellation_rate = 100*(1 - Cum_Number_maintained/Cum_Claim_count),
        Cum_Results = Cum_Premium - Cum_Claims_amts,
        Cum_LR = 100*Cum_Claims_amts/Cum_Premium
      )
    data
  })
  
  output$dynamic_title_claims_1 <- renderUI({
    paste0(input$Variable_choice_Id_1," per month over year (Year-To-Date)")
  })
  

  output$claims_per_month_year <- plotly::renderPlotly({
    if(!input$cumulated_choice_Id1){
    plot_ly(Variables_MonthYear(), x = ~Month, y = ~get(input$Variable_choice_Id_1), color = ~factor(Year), type = 'scatter', mode = 'lines+markers') %>%
      layout(xaxis = list(title = 'Month', tickmode = 'linear', dtick = 1),
             yaxis = list(title = 'Value'),
             legend = list(title = list(text = 'Year'),
                           orientation = 'h',  # Horizontal orientation
                           x = 0.5,  # Centered horizontally
                           xanchor = 'center',  # Anchor legend at the center of x
                           y = 1.1  # Position above the plot (adjust as needed)
             )
             )
    } else {
      plot_ly(Variables_MonthYear(), x = ~Month, y = ~get(paste0('Cum_',input$Variable_choice_Id_1)), color = ~factor(Year), type = 'scatter', mode = 'lines+markers') %>%
        layout(xaxis = list(title = 'Month', tickmode = 'linear', dtick = 1),
               yaxis = list(title = 'Cumulative value'),
               legend = list(title = list(text = 'Year'),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               )
        )
    }
  })
  
  # Render claims amounts per weekofyear
  Variables_WeekOfYear <- shiny::reactive({
    req(df_claims_underwriting)
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(
          weekofyear = lubridate::week(as.Date(uw_date)),
          year = lubridate::year(as.Date(uw_date)))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(
          weekofyear = lubridate::week(as.Date(activity_start_date)),
          year = lubridate::year(as.Date(activity_start_date)))
    }
    
    # Computing indicator and variables
    data <- df_data %>% 
      dplyr::group_by(year,weekofyear) %>% 
      dplyr::summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE)
      ) %>% 
      dplyr::arrange(year,weekofyear) %>%
      dplyr::group_by(year) %>%
      dplyr::mutate(
        Cum_Claim_count = cumsum(Claim_count),
        Cum_Policy_count = cumsum(Policy_count),
        Cum_Premium = cumsum(Premium),
        Cum_Claims_amts = cumsum(Claims_amts),
        Cum_Number_maintained = cumsum(Number_maintained)
      ) %>% 
      dplyr::mutate(
        Frequency = 100*(Claim_count/Policy_count),
        Cancellation_rate = 100*(1 - Number_maintained/Claim_count),
        Results = Premium - Claims_amts,
        LR = 100*Claims_amts/Premium,
        Cum_Frequency = 100*(Cum_Claim_count/Cum_Policy_count),
        Cum_Cancellation_rate = 100*(1 - Cum_Number_maintained/Cum_Claim_count),
        Cum_Results = Cum_Premium - Cum_Claims_amts,
        Cum_LR = 100*Cum_Claims_amts/Cum_Premium
      )
    data
  })
  
  output$dynamic_title_claims_2 <- renderUI({
    paste0(input$Variable_choice_Id_2," per weekofyear over year (Year-To-Date)")
  })
  
  output$claims_per_weekof_year <- plotly::renderPlotly({
    if(!input$cumulated_choice_Id2){
      plot_ly(Variables_WeekOfYear(), x = ~weekofyear, y = ~get(input$Variable_choice_Id_2), color = ~factor(year), type = 'scatter', mode = 'lines+markers') %>%
        layout(xaxis = list(title = 'Week of year', tickmode = 'linear', dtick = 3),
               yaxis = list(title = 'Value'),
               legend = list(title = list(text = 'Year'),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               )
        )
    } else {
      plot_ly(Variables_WeekOfYear(), x = ~weekofyear, y = ~get(paste0('Cum_',input$Variable_choice_Id_2)), color = ~factor(year), type = 'scatter', mode = 'lines+markers') %>%
        layout(xaxis = list(title = 'Week of year', tickmode = 'linear', dtick = 3),
               yaxis = list(title = 'Cumulative'),
               legend = list(title = list(text = 'Year'),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               )
        )
    }
  })
  
  # render top 10 claims
  top_10_claims <- reactive({
   df <- df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::arrange(desc(amount_of_claims)) %>%
      dplyr::select(policy_id,date_of_claim,amount_of_claims,activity_start_date) %>% 
      head(10) 
   
   if(isolate(input$date_type_id) == "Underwriting"){
     df %>% 
      transmute(
        name = date_of_claim,
        y = amount_of_claims,
        policy_id = policy_id
      )
   } else {
     df %>% 
       transmute(
         name = activity_start_date,
         y = amount_of_claims,
         policy_id = policy_id
       )
   }
  })
  
  output$top_ten_claims <- highcharter::renderHighchart({
    highchart() %>%
      hc_chart(type = "bar") %>%
      hc_xAxis(categories = top_10_claims()$name) %>%
      hc_yAxis(title = list(text = "Amount")) %>%
      hc_series(list(
        name = "Claims",
        data = purrr::transpose(top_10_claims()),
        color = "red"  # Set bar color to red
      )) %>%
      hc_tooltip(pointFormat = '<b>Policy id: {point.policy_id}</b>') %>%
      hc_plotOptions(bar = list(dataLabels = list(enabled = TRUE)))
  })
  
  # render distribution partners 
  data_partners_distribution <- shiny::reactive({
    data <- df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1 )
    data %>% 
      dplyr::group_by(company_name) %>% 
      summarise(
        Percentage_nb_claims = round(n() / nrow(data) * 100,2), 
        Percentage_amts_claims = round(sum(amount_of_claims)/df_claims_underwriting()[,sum(amount_of_claims)]* 100,2)
      )
  })
  
  output$Partners_distribution <- highcharter::renderHighchart({
    
    nb_claims = df_claims_underwriting()[,sum(frequency)]
    Claims_amts = round(df_claims_underwriting()[,sum(amount_of_claims)],0)
    
    if(input$partners_var_id == "Number of claims"){
      customLabel <- glue::glue("Claims<br/><strong> {nb_claims}</strong>")
      
      data <- data_partners_distribution() %>% 
        transmute(
          company_name,
          Percentage = Percentage_nb_claims
        )
    } else{
      customLabel <- glue::glue("Claims amount<br/><strong> {Claims_amts}€</strong>")
      
      data <- data_partners_distribution() %>% 
        transmute(
          company_name,
          Percentage = Percentage_amts_claims
        )
    }

    data %>% 
      hchart(type =  "pie",hcaes(company_name,Percentage),innerSize = '75%') %>% 
      hc_chart(type = "pie", plotBorderWidth = NULL, plotShadow = FALSE,events = list(
        render = JS(paste0("
      function() {
        var chart = this,
            series = chart.series[0];
        var customLabel = chart.options.customLabel;

        if (!customLabel) {
          customLabel = chart.options.customLabel =
            chart.renderer.label('", customLabel, "')
              .css({
                color: '#000',
                textAnchor: 'middle'
              })
              .add();
        }

        var x = series.center[0] + chart.plotLeft,
            y = series.center[1] + chart.plotTop - (customLabel.getBBox().height / 2);

        customLabel.attr({
          x: x,
          y: y
        });

        // Set font size based on chart diameter
        customLabel.css({
          fontSize: series.center[2] / 12 + 'px'
        });
      }
    "))
      )) %>%  # Set type to "pie" for donut chart
      hc_subtitle(text = "In % of total") %>%
      hc_plotOptions(pie = list(
        innerSize = "50%",  # Inner size determines the size of the hole (donut effect)
        allowPointSelect = TRUE,
        cursor = "pointer",
        dataLabels = list(enabled = TRUE, format = "{point.name}: {point.percentage:.1f}%")
      )) 
  })
  
 
  ## Render claim amts by partners over times
  barChartRace_claim_amts <- reactive({
    req(df_claims_underwriting)
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(uw_date, "%Y-%m") )
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(activity_start_date, "%Y-%m") )
    }
    
    df_data %>% 
      dplyr::group_by(company_name,YearMonth) %>% 
      dplyr::summarise(
        Claims_amt = sum(amount_of_claims)
      ) %>%
      arrange(company_name,YearMonth) %>% 
      group_by(company_name) %>% 
      dplyr::mutate(
        Cum_Claims_amt = cumsum(Claims_amt)
      )
      
  })
  
  output$barChartRace_claim_amts <- echarts4r::renderEcharts4r({
    barChartRace_claim_amts() %>% 
    group_by(YearMonth) %>%
      e_charts(company_name, timeline = TRUE) %>%
      e_bar(Claims_amt, realtimeSort = TRUE, itemStyle = list(
        borderColor = "#FAFAFA", borderWidth = '0.1', color = "#FF5733", borderRadius = 2)
      ) %>%
      e_legend(show = FALSE) %>%
      e_flip_coords() %>%
      e_y_axis(inverse = TRUE) %>%
      e_labels(position = "insideRight", 
               formatter = htmlwidgets::JS("
        function(params){
          return(Math.round(params.value[0]))
        }
      ")) %>%
      e_timeline_opts(autoPlay = TRUE, top = "55") %>%
      e_grid(top = 100)
  })
  
  ## Render performance indicator by table
  Performance_indicator_by_partners <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      group_by(company_name) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        company_name,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      )
  })
  output$Performance_indicator_by_partners <- DT::renderDT({
    DT::datatable(Performance_indicator_by_partners(), options = list(
      pageLength = 10,
      autoWidth = TRUE,
      dom = 'Bfrtip',
      buttons = c('copy', 'csv', 'excel', 'pdf', 'print'),
      initComplete = JS(
        "function(settings, json) {",
        "$(this.api().table().header()).css({'background-color': '#d3d3d3', 'color': '#000'});",
        "}"
      )
    ), extensions = 'Buttons', rownames = FALSE) 
    
  })
  
  ## Render cancellation rate per partners
  claims_amount_by_partners <- reactive({
    df_claims_underwriting() %>%
      group_by(company_name) %>%
      summarise(TotalClaimAmount = sum(amount_of_claims)) %>%
      arrange(desc(TotalClaimAmount))
  })
  
  output$claims_amount_by_partners <- highcharter::renderHighchart({
    Performance_indicator_by_partners() %>% 
      hchart(
        "column",
        hcaes(x = company_name, y = Cancellation_rate),
        color = "red",
        name = "Cancellation rate"
      )
  })
  
  ## Render Distribution channel distribution
  Distribution_channel_dist <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::group_by(distribution_chanel) %>% 
      dplyr::summarise(
        Claim_count = n(),
        Claim_amount = sum(amount_of_claims)
      )
  })
  
  
  output$Distribution_channel_dist <-  renderAmCharts({
    if(input$distribution_channel_var_id == "Claims amount"){
    data <- Distribution_channel_dist() %>% 
      dplyr::transmute(
        label = distribution_chanel,
        value = Claim_amount
      )
    } else {
      data <- Distribution_channel_dist() %>% 
        dplyr::transmute(
          label = distribution_chanel,
          value = Claim_count
        )
    }
    rAmCharts::amPie(data = data) %>% 
      amOptions( main = paste0("In % of ",input$distribution_channel_var_id),
                 mainColor = "#FF5733")
  })
  
  ## Render activity type
  Activity_dist <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::group_by(category) %>% 
      dplyr::summarise(
        Claim_count = n(),
        Claim_amount = sum(amount_of_claims)
      )
  })
  
  
  output$Activity_dist <-  renderAmCharts({
    if(input$activity_var_id == "Claims amount"){
      data <- Activity_dist() %>% 
        dplyr::transmute(
          label = category,
          value = Claim_amount
        )
    } else {
      data <- Activity_dist() %>% 
        dplyr::transmute(
          label = category,
          value = Claim_count
        )
    }
    rAmCharts::amPie(data = data) %>% 
      amOptions( main = paste0("In % of ",input$activity_var_id),
                 mainColor = "#FF5733")
  })
  
  ## Peril activity type
  peril_dist <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::group_by(weather_guarantee_type) %>% 
      dplyr::summarise(
        Claim_count = n(),
        Claim_amount = sum(amount_of_claims)
      )
  })
  
  
  output$peril_dist <-  renderAmCharts({
    if(input$peril_var_id == "Claims amount"){
      data <- peril_dist() %>% 
        dplyr::transmute(
          label = weather_guarantee_type,
          value = Claim_amount
        )
    } else {
      data <- peril_dist() %>% 
        dplyr::transmute(
          label = weather_guarantee_type,
          value = Claim_count
        )
    }
    rAmCharts::amPie(data = data) %>% 
      amOptions( main = paste0("In % of ",input$peril_var_id),
                 mainColor = "#FF5733")
  })
  
  ## Render peril by month variable (choix)
  peril_by_month <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(uw_date,"%B"),levels = month.name))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(activity_start_date, "%B"),levels = month.name))
    }
    
    df_data %>% 
      group_by(weather_guarantee_type,Month) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        weather_guarantee_type,
        Month,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      ) %>% 
      ungroup %>% 
      dplyr::group_by(Month) %>% 
      dplyr::mutate(
        Percentage_Claims_amts = 100*Claims_amts/sum(Claims_amts),
        Percentage_Claim_count = 100*Claim_count/sum(Claim_count),
        Percentage_Frequency = 100*Frequency/sum(Frequency),
        Percentage_Cancellation_rate = 100*Cancellation_rate/sum(Cancellation_rate),
        Percentage_Results = 100*Results/sum(Results),
        Percentage_LR = 100*LR/sum(LR),
        Percentage_Destruction_rate = 100*Destruction_rate/sum(Destruction_rate)
      )
  })
  
  output$dynamic_title_channel_peril_1 <- renderUI({
    stacked = if(input$stacked_choice_Id1_peril){
      "Stacked (in %) "
    } else {
      ""
    }
    paste0(stacked,input$peril_var2," by month")
  })
  
  output$peril_by_month <- plotly::renderPlotly({
    if(input$stacked_choice_Id1_peril){
      plot_ly(peril_by_month(), x = ~Month, y = ~get(paste0("Percentage_",input$peril_var2)), color = ~weather_guarantee_type, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", weather_guarantee_type, "<br>",
                            "Value: ", get(paste0("Percentage_",input$peril_var2))),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ),barmode = "stack")
    } else {
      plot_ly(peril_by_month(), x = ~Month, y = ~get(input$peril_var2), color = ~weather_guarantee_type, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", weather_guarantee_type, "<br>",
                            "Value: ", get(input$peril_var2)),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ))
    }
  })
  
  ## Render claims amount per activity
  claims_amount_by_activity <- reactive({
    df_claims_underwriting() %>%
      group_by(category) %>%
      summarise(TotalClaimAmount = sum(amount_of_claims)) %>%
      arrange(desc(TotalClaimAmount))
  })
  
  output$claims_amount_by_activity <- highcharter::renderHighchart({
    claims_amount_by_activity() %>% 
      hchart(
        "column",
        hcaes(x = category, y = TotalClaimAmount),
        color = "red",
        name = "Claims amount"
      )
  })
  
  ## Render claim amts by activity over times
  barChartRace_claim_activity <- reactive({
    req(df_claims_underwriting)
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(uw_date, "%Y-%m") )
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(YearMonth = format(activity_start_date, "%Y-%m") )
    }
    
    df_data %>% 
      dplyr::group_by(category,YearMonth) %>% 
      dplyr::summarise(
        Claims_amt = sum(amount_of_claims)
      ) %>%
      arrange(category,YearMonth) %>% 
      group_by(category) %>% 
      dplyr::mutate(
        Cum_Claims_amt = cumsum(Claims_amt)
      )
    
  })
  
  output$barChartRace_claim_activity <- echarts4r::renderEcharts4r({
    barChartRace_claim_activity() %>% 
      group_by(YearMonth) %>%
      e_charts(category, timeline = TRUE) %>%
      e_bar(Claims_amt, realtimeSort = TRUE, itemStyle = list(
        borderColor = "#FAFAFA", borderWidth = '0.1', color = "#FF5733", borderRadius = 2)
      ) %>%
      e_legend(show = FALSE) %>%
      e_flip_coords() %>%
      e_y_axis(inverse = TRUE) %>%
      e_labels(position = "insideRight", 
               formatter = htmlwidgets::JS("
        function(params){
          return(Math.round(params.value[0]))
        }
      ")) %>%
      e_timeline_opts(autoPlay = TRUE, top = "55") %>%
      e_grid(top = 100)
  })
  
  ## Render performance indicator by activity
  Performance_indicator_by_activity <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      group_by(category) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        category,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      )
  })
  output$Performance_indicator_by_activity <- DT::renderDT({
    DT::datatable(Performance_indicator_by_activity(), options = list(
      pageLength = 10,
      autoWidth = TRUE,
      dom = 'Bfrtip',
      buttons = c('copy', 'csv', 'excel', 'pdf', 'print'),
      initComplete = JS(
        "function(settings, json) {",
        "$(this.api().table().header()).css({'background-color': '#d3d3d3', 'color': '#000'});",
        "}"
      )
    ), extensions = 'Buttons', rownames = FALSE) 
    
  })
  
  ## Threshold type
  Threshold_dist <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::group_by(threshold_trigger) %>% 
      dplyr::summarise(
        Claim_count = n(),
        Claim_amount = sum(amount_of_claims)
      )
  })
  
  
  output$Threshold_dist <-  renderAmCharts({
    if(input$Threshold_var_id == "Claims amount"){
      data <- Threshold_dist() %>% 
        dplyr::transmute(
          label = threshold_trigger,
          value = Claim_amount
        )
    } else {
      data <- Threshold_dist() %>% 
        dplyr::transmute(
          label = threshold_trigger,
          value = Claim_count
        )
    }
    rAmCharts::amPie(data = data) %>% 
      amOptions( main = paste0("In % of ",input$Threshold_var_id),
                 mainColor = "#FF5733")
  })
  
  
  ## Render Threshold by month variable (choix)
  Threshold_by_month <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(uw_date,"%B"),levels = month.name))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(activity_start_date, "%B"),levels = month.name))
    }
    
    df_data %>% 
      group_by(threshold_trigger,Month) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        threshold_trigger,
        Month,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      ) %>% 
      ungroup %>% 
      dplyr::group_by(Month) %>% 
      dplyr::mutate(
        Percentage_Claims_amts = 100*Claims_amts/sum(Claims_amts),
        Percentage_Claim_count = 100*Claim_count/sum(Claim_count),
        Percentage_Frequency = 100*Frequency/sum(Frequency),
        Percentage_Cancellation_rate = 100*Cancellation_rate/sum(Cancellation_rate),
        Percentage_Results = 100*Results/sum(Results),
        Percentage_LR = 100*LR/sum(LR),
        Percentage_Destruction_rate = 100*Destruction_rate/sum(Destruction_rate)
      )
  })
  
  output$dynamic_title_channel_Threshold_1 <- renderUI({
    stacked = if(input$stacked_choice_Id1_Threshold){
      "Stacked (in %) "
    } else {
      ""
    }
    paste0(stacked,input$Threshold_var2," by month")
  })
  
  output$Threshold_by_month <- plotly::renderPlotly({
    if(input$stacked_choice_Id1_Threshold){
      plot_ly(Threshold_by_month(), x = ~Month, y = ~get(paste0("Percentage_",input$Threshold_var2)), color = ~threshold_trigger, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", threshold_trigger, "<br>",
                            "Value: ", get(paste0("Percentage_",input$Threshold_var2))),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ),barmode = "stack")
    } else {
      plot_ly(Threshold_by_month(), x = ~Month, y = ~get(input$Threshold_var2), color = ~threshold_trigger, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", threshold_trigger, "<br>",
                            "Value: ", get(input$Threshold_var2)),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ))
    }
  })
  
  ## Trigger type
  Trigger_dist <- reactive({
    req(df_claims_underwriting)
    
    df_claims_underwriting() %>% 
      dplyr::filter(frequency == 1) %>% 
      dplyr::group_by(trigger_type) %>% 
      dplyr::summarise(
        Claim_count = n(),
        Claim_amount = sum(amount_of_claims)
      )
  })
  
  
  output$Trigger_dist <-  renderAmCharts({
    if(input$Trigger_var_id == "Claims amount"){
      data <- Trigger_dist() %>% 
        dplyr::transmute(
          label = trigger_type,
          value = Claim_amount
        )
    } else {
      data <- Trigger_dist() %>% 
        dplyr::transmute(
          label = trigger_type,
          value = Claim_count
        )
    }
    rAmCharts::amPie(data = data) %>% 
      amOptions( main = paste0("In % of ",input$Trigger_var_id),
                 mainColor = "#FF5733")
  })
  
  ## Render Trigger by month variable (choix)
  trigger_by_month <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(uw_date,"%B"),levels = month.name))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(activity_start_date, "%B"),levels = month.name))
    }
    
    df_data %>% 
      group_by(trigger_type,Month) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        trigger_type,
        Month,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      ) %>% 
      ungroup %>% 
      dplyr::group_by(Month) %>% 
      dplyr::mutate(
        Percentage_Claims_amts = 100*Claims_amts/sum(Claims_amts),
        Percentage_Claim_count = 100*Claim_count/sum(Claim_count),
        Percentage_Frequency = 100*Frequency/sum(Frequency),
        Percentage_Cancellation_rate = 100*Cancellation_rate/sum(Cancellation_rate),
        Percentage_Results = 100*Results/sum(Results),
        Percentage_LR = 100*LR/sum(LR),
        Percentage_Destruction_rate = 100*Destruction_rate/sum(Destruction_rate)
      )
  })
  
  output$dynamic_title_channel_trigger_1 <- renderUI({
    stacked = if(input$stacked_choice_Id1_trigger){
      "Stacked (in %) "
    } else {
      ""
    }
    paste0(stacked,input$trigger_var2," by month")
  })
  
  output$trigger_by_month <- plotly::renderPlotly({
    if(input$stacked_choice_Id1_trigger){
      plot_ly(trigger_by_month(), x = ~Month, y = ~get(paste0("Percentage_",input$trigger_var2)), color = ~trigger_type, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", trigger_type, "<br>",
                            "Value: ", get(paste0("Percentage_",input$trigger_var2))),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ),barmode = "stack")
    } else {
      plot_ly(trigger_by_month(), x = ~Month, y = ~get(input$trigger_var2), color = ~trigger_type, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", trigger_type, "<br>",
                            "Value: ", get(input$trigger_var2)),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ))
    }
  })
  
  ## Render claims amount Year/Month
  claims_amount_by_month_Year_ID <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(
          Year = lubridate::year(uw_date),
          Month = factor(format(uw_date,"%B"),levels = month.name))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(
          Year = lubridate::year(activity_start_date),
          Month = factor(format(activity_start_date, "%B"),levels = month.name))
    }
    
    df_data %>% 
      group_by(Year,Month) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        Year,
        Month,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      ) 
  })
  
  claims_amount_by_Year_ID <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(
          Year = lubridate::year(uw_date))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(
          Year = lubridate::year(activity_start_date))
    }
    
    df_data %>% 
      group_by(Year) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        Year,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      )
  })
  
  output$claims_amount_by_month_Year_ID <- highcharter::renderHighchart({
    
    x <- c("Claims amount:","Number of claims:","Frequency:","Cancellation rate:",
           "Results:","Loss Ratio (LR):","Destruction Rate (DR):")
    y <- c("€{point.Claims_amts}", "{point.Claim_count}", "{point.Frequency}%","{point.Cancellation_rate}%",
           "€{point.Results}","{point.LR}%","{point.Destruction_rate}%")
    
    tt <- tooltip_table(x, y)
    
    
    drilldown_chart_data <- claims_amount_by_month_Year_ID() %>%
      ungroup() %>% 
      group_nest(Year) %>%
      mutate(
        id = Year,
        type = "column",
        data = map(data, mutate, name = Month, y = !!sym(input$monthYear_var2)),
        data = map(data, list_parse)
      )
    
    hchart(
      claims_amount_by_Year_ID(),
      "column",
      hcaes(x = Year, name = Year, y = !!sym(input$monthYear_var2), drilldown = Year),
      name = input$monthYear_var2,
      colorByPoint = TRUE
    ) |>
      hc_drilldown(
        allowPointDrilldown = TRUE,
        series = list_parse(drilldown_chart_data)
      )|>
      hc_tooltip(
        pointFormat = tt, # "{point.name} {point.pop}"
        useHTML = TRUE,
        valueDecimals = 0
      ) |>
    hc_xAxis(
      title = ""
    )
    
  })

  
  ## Render distribution channel variable (choix)
  distribution_chanel_by_month <- reactive({
    req(df_claims_underwriting)
    
    if(isolate(input$date_type_id) == "Underwriting"){
      df_data  = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(uw_date,"%B"),levels = month.name))
    } else {
      df_data = df_claims_underwriting() %>%  
        dplyr::mutate(Month = factor(format(activity_start_date, "%B"),levels = month.name))
    }
    
    df_data %>% 
      group_by(distribution_chanel,Month) %>% 
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        distribution_chanel,
        Month,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      ) %>% 
       ungroup %>% 
      dplyr::group_by(Month) %>% 
      dplyr::mutate(
        Percentage_Claims_amts = 100*Claims_amts/sum(Claims_amts),
        Percentage_Claim_count = 100*Claim_count/sum(Claim_count),
        Percentage_Frequency = 100*Frequency/sum(Frequency),
        Percentage_Cancellation_rate = 100*Cancellation_rate/sum(Cancellation_rate),
        Percentage_Results = 100*Results/sum(Results),
        Percentage_LR = 100*LR/sum(LR),
        Percentage_Destruction_rate = 100*Destruction_rate/sum(Destruction_rate)
      )
  })
  
  output$dynamic_title_channel_dist_1 <- renderUI({
    stacked = if(input$stacked_choice_Id1){
      "Stacked (in %) "
    } else {
      ""
    }
    paste0(stacked,input$distribution_channel_var2," by month")
  })
  
  output$distribution_chanel_by_month <- plotly::renderPlotly({
    if(input$stacked_choice_Id1){
      plot_ly(distribution_chanel_by_month(), x = ~Month, y = ~get(paste0("Percentage_",input$distribution_channel_var2)), color = ~distribution_chanel, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", distribution_chanel, "<br>",
                            "Value: ", get(paste0("Percentage_",input$distribution_channel_var2))),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ),barmode = "stack")
    } else {
      plot_ly(distribution_chanel_by_month(), x = ~Month, y = ~get(input$distribution_channel_var2), color = ~distribution_chanel, type = "bar", 
              hoverinfo = "text",
              text = ~paste("Month: ", Month, "<br>",
                            "Variable: ", distribution_chanel, "<br>",
                            "Value: ", get(input$distribution_channel_var2)),
              colors = c("#1f77b4", "#ff7f0e")) %>%
        layout(xaxis = list(title = "Month"),
               yaxis = list(title = "Value"),
               legend = list(title = list(text = ''),
                             orientation = 'h',  # Horizontal orientation
                             x = 0.5,  # Centered horizontally
                             xanchor = 'center',  # Anchor legend at the center of x
                             y = 1.1  # Position above the plot (adjust as needed)
               ))
    }
  })
  
  
  ## Render map claims amount per region
  variable_by_region_dept <- reactive({
    df_claims_underwriting() %>% 
      dplyr::left_join(
        dm$dep_corres %>% select(code_insee_dep,code_insee_reg), by = c("departement" = "code_insee_dep")
      ) %>% 
      dplyr::group_by(departement,code_insee_reg) %>%
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        departement,
        code_insee_reg,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      )
  })
  
  
  ## Render map Loss ratio (LR) per department
  output$variable_by_departement <- highcharter::renderHighchart({
    x <- c("Claims amount:","Number of claims:","Frequency:","Cancellation rate:",
           "Results:","Loss Ratio (LR):","Destruction Rate (DR):")
    y <- c("€{point.Claims_amts2}", "{point.Claim_count2}", "{point.Frequency2}%","{point.Cancellation_rate2}%",
           "€{point.Results2}","{point.LR2}%","{point.Destruction_rate2}%")
    
    tt <- tooltip_table(x, y)
    
    data_dep <- variable_by_region_dept() %>%
      left_join(dm$dep_corres, by = c('departement' = 'code_insee_dep')) %>% 
      mutate(LR2 = LR, Claims_amts2 = Claims_amts, Claim_count2 = Claim_count,
             Frequency2 = Frequency,Cancellation_rate2 = Cancellation_rate,
             Results2 = Results, Destruction_rate2 = Destruction_rate)

    hcmap("countries/fr/fr-all-all",showInLegend = FALSE,data = data_dep, value = input$departement_var2,
          joinBy = c("hc-a2","hc_a2"), name = input$departement_var2,
          dataLabels = list(enabled = TRUE, format = '{point.name}'),
          borderColor = "#FAFAFA", borderWidth = 0.1,
          tooltip = list(valueDecimals = 2, valuePrefix = "€", valueSuffix = " EURO")) %>%
          hc_colorAxis(minColor = brewer.pal(8,"Reds")[1], maxColor = brewer.pal(8,"Reds")[8]) %>%
          hc_mapNavigation(enabled = TRUE) %>% 
          hc_exporting(enabled = TRUE, formAttributes = list(target = "_blank")) %>% 
      hc_tooltip(
        pointFormat = tt, # "{point.name} {point.pop}"
        useHTML = TRUE,
        valueDecimals = 0
      )

  })
  
  ## render map region
  variable_by_region <- reactive({
    df_claims_underwriting() %>% 
      dplyr::left_join(
        dm$dep_corres %>% select(code_insee_dep,code_insee_reg), by = c("departement" = "code_insee_dep")
      ) %>% 
      dplyr::group_by(code_insee_reg) %>%
      summarise(
        Claim_count = sum(frequency),
        Policy_count = n(),
        Premium = sum(activity_price*orakle_price_ratio),
        Claims_amts = sum(amount_of_claims),
        Number_maintained = sum(activity_maintained,na.rm = TRUE),
        Sum_insured = sum(activity_price)
      ) %>% 
      dplyr::transmute(
        code_insee_reg,
        Claims_amts,
        Claim_count,
        Frequency = round(100*(Claim_count/Policy_count),1),
        Cancellation_rate = round(100*(1 - Number_maintained/Claim_count),1),
        Results = round(Premium - Claims_amts,1),
        LR = round(100*Claims_amts/Premium,1),
        Destruction_rate = round(100*Claims_amts/Sum_insured,1)
      )
  })
  
  output$variable_by_region <- highcharter::renderHighchart({
    x <- c("Claims amount:","Number of claims:","Frequency:","Cancellation rate:",
           "Results:","Loss Ratio (LR):","Destruction Rate (DR):")
    y <- c("€{point.Claims_amts2}", "{point.Claim_count2}", "{point.Frequency2}%","{point.Cancellation_rate2}%",
           "€{point.Results2}","{point.LR2}%","{point.Destruction_rate2}%")
    
    tt <- tooltip_table(x, y)
    
    data_reg <- variable_by_region() %>%
      left_join(dm$reg_corres) %>% 
      mutate(LR2 = LR, Claims_amts2 = Claims_amts, Claim_count2 = Claim_count,
             Frequency2 = Frequency,Cancellation_rate2 = Cancellation_rate,
             Results2 = Results, Destruction_rate2 = Destruction_rate)

    hcmap("countries/fr/fr-all",data = data_reg, value = input$region_var2,
          joinBy = c("hc-a2"), name = input$region_var2,
          dataLabels = list(enabled = TRUE, format = '{point.name}'),
          borderColor = "#FAFAFA", borderWidth = 0.1,
          tooltip = list(valueDecimals = 2, valuePrefix = "€", valueSuffix = " EURO")) %>%
      hc_colorAxis(minColor = brewer.pal(8,"Reds")[1], maxColor = brewer.pal(8,"Reds")[8]) %>%
      hc_mapNavigation(enabled = TRUE) %>%
      hc_exporting(enabled = TRUE, formAttributes = list(target = "_blank")) %>% 
      hc_tooltip(
        pointFormat = tt, # "{point.name} {point.pop}"
        useHTML = TRUE,
        valueDecimals = 0
      )

  })

  #############################################################################################################################################################
                                                                          ### Arnaud
  
  # Importations de toutes les données
  
  df_forecast_local <- jsonlite::fromJSON("www/forecast_07-07-2025.JSON", flatten = TRUE)
  df_forecast_local <- as.data.table(df_forecast_local)
  
  df_claims_local <- jsonlite::fromJSON("www/claims 07-07-2025.JSON", flatten = TRUE)
  df_claims_local <- as.data.table(df_claims_local)
  
  token <- "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************.XymknjsuMrJ63jw4vrSdWsF56h6pYSeDtQDunNtuf04"
  url_forecast <- "http://*************:8000/api/trips/forecasts"
  df_forecast_serveur <-  GET(url_forecast, add_headers(Authorization = paste("Bearer", token)))
  df_forecast_serveur <- content(df_forecast_serveur, as = "text", encoding = "UTF-8")
  df_forecast_serveur <- jsonlite::fromJSON(df_forecast_serveur, flatten = TRUE)
  df_forecast_serveur <- as.data.table(df_forecast_serveur)
  
  url_claims <- "http://*************:8000/api/trips/claims"
  df_claims_serveur <-  GET(url_claims, add_headers(Authorization = paste("Bearer", token)))
  df_claims_serveur <- content(df_claims_serveur, as = "text", encoding = "UTF-8")
  df_claims_serveur <- jsonlite::fromJSON(df_claims_serveur, flatten = TRUE)
  df_claims_serveur <- as.data.table(df_claims_serveur)
  
  # Observer to show loading screen when data type changes
  observeEvent(input$data_type_id, {
    if (!is.null(input$data_type_id)) {
      waiter_screen <- waiter::Waiter$new(
        html = tagList(
          waiter::spin_fading_circles(),
          h3(paste("Chargement des données", input$data_type_id, "..."), style = "color: white;")
        ),
        color = waiter::transparent(.7)
      )
      waiter_screen$show()

      # Hide the loading screen after a short delay to allow data processing
      shiny::invalidateLater(1000)
      waiter_screen$hide()
    }
  })

  donnees <- reactive({

    if (input$data_type_id == "Local"){
      df_forecast <- copy(df_forecast_local)
      df_claims <- copy(df_claims_local)
    } else {
      df_forecast <- copy(df_forecast_serveur)
      df_claims <- copy(df_claims_serveur)
    }
    
      setnames(df_forecast, old = names(df_forecast), new = gsub("^[^.]*\\.[^.]*\\.", "", names(df_forecast)))
      df_forecast$trigger_hour_inf <- sapply(df_forecast$subcoverage_trips, function(x) x$trigger_hour_inf)
      df_forecast$trigger_hour_inf <- as.numeric(gsub(",", ".", df_forecast$trigger_hour_inf))
      df_forecast$trigger_number_hours <- sapply(df_forecast$subcoverage_trips, function(x) x$trigger_number_hours)
      df_forecast$lump_sum_maintain <- sapply(df_forecast$subcoverage_trips, function(x) x$lump_sum_maintain)
      df_forecast$is_lump_sum <- sapply(df_forecast$subcoverage_trips, function(x) x$is_lump_sum)
      
      df_forecast$activity_price <- as.numeric(gsub(",", ".", gsub(" EUR", "", df_forecast$activity_price)))
      df_forecast$global_siv_ratio_maintained <- as.numeric(gsub(",", ".", df_forecast$global_siv_ratio_maintained))
      df_forecast$lump_sum_maintain <- as.numeric(gsub(",", ".", df_forecast$lump_sum_maintain))
      df_forecast$trip_day <- as.Date(df_forecast$trip_day, format = "%d/%m/%Y")
      df_forecast$trigger_hour_ing <- as.numeric(gsub(",", ".", df_forecast$trigger_hour_inf))
      df_forecast$latitude <- round(as.numeric(gsub(",", ".", df_forecast$latitude)),1)
      df_forecast$longitude <- round(as.numeric(gsub(",", ".", df_forecast$longitude)),1)
      
      dups <- duplicated(as.list(names(df_forecast)))
      df_forecast <- df_forecast[, .SD, .SDcols = !dups]
      
      df_forecast <- df_forecast %>%
        mutate(trip_effective_start_date = if_else(
          company_name == "Flower",
          as.Date(activity_start_date, format = "%d/%m/%Y") + map_dbl(subcoverage_trips, ~ .x$effective_start_date),
          as.Date(trip_effective_start_date, format = "%d/%m/%Y")
        ))
      
      
      df_forecast <- df_forecast %>%
        mutate(trip_effective_end_date = if_else(
          company_name == "Flower",
          as.Date(activity_end_date, format = "%d/%m/%Y") + map_dbl(subcoverage_trips, ~ .x$effective_end_date),
          as.Date(trip_effective_end_date, format = "%d/%m/%Y")
        ))

      
      df_forecast <- df_forecast %>%
        mutate(lump_sum_maintain = if_else(
          is_lump_sum == FALSE,
          activity_price * global_siv_ratio_maintained/100/(as.numeric(as.Date(activity_end_date, format = "%d/%m/%Y")-as.Date(activity_start_date, format = "%d/%m/%Y"))),
          lump_sum_maintain
        ))
      
      setnames(df_claims, old = names(df_claims), new = sub(".*\\.", "", names(df_claims)))
      dups2 <- duplicated(as.list(names(df_claims)))
      df_claims <- df_claims[, .SD, .SDcols = !dups2]
      
      df_claims <- df_claims %>%
        mutate(
          claims = lapply(claims, \(df) df[, c("date_of_claim", "amount_of_claim", "claim_id"), drop = FALSE])
        ) %>%
        unnest(claims)
      
      df_claims <- df_claims %>% 
        rename(
          "trip_day" = "date_of_claim",
          "claim_amount" = "amount_of_claim" )
      
      df_claims$trip_day <- as.Date(df_claims$trip_day,format = "%Y-%m-%d")
      
      donnees_completes <- merge(df_forecast, df_claims, by = c("trip_day", "policy_code","policy_id"), all.x = TRUE)
      donnees_completes$claim_id[is.na(donnees_completes$claim_id)] <- "Pas de claim id car pas de sinistre"
      dups3 <- duplicated(as.list(names(donnees_completes)))
      donnees_completes <- donnees_completes[, .SD, .SDcols = !dups3]
      dups4 <- duplicated(as.list(donnees_completes))
      donnees_completes <- donnees_completes[, .SD, .SDcols = !dups4]
      donnees_completes <- donnees_completes %>% select(-ends_with(".y"))
      names(donnees_completes) <- sub("\\.x$", "", names(donnees_completes))
      
      
      donnees_completes <- donnees_completes %>% rename(
        "activity_name" = "name",
        "client_country" = "country",
        "activity_category" = "category",
        "partner_id" = "activity_partner_id"
      )
      
      donnees_completes$date_run_utc <- paste(donnees_completes$meteomatics_init_year,donnees_completes$meteomatics_init_month,
                                              donnees_completes$meteomatics_init_day,donnees_completes$meteomatics_run_hour_utc, sep = "/"
      )
      
      donnees_completes$combined_address <- paste(donnees_completes$activity_address,donnees_completes$activity_zip_code,
                                                  donnees_completes$activity_city,donnees_completes$activity_country, sep = ", "
      )
      donnees_completes <- donnees_completes %>%
        select(-activity_address, -activity_zip_code, -activity_city, -activity_country)
      
      donnees_completes <- donnees_completes %>%
        select(-meteomatics_init_year, -meteomatics_init_month, -meteomatics_init_day, -meteomatics_run_hour_utc)
      
      donnees_completes <- donnees_completes %>%
        mutate(
          nb_hour_bad_weather = map2_int(weather_data, trigger_hour_inf, ~ {
            vals <- as.numeric(gsub(",", ".", .x$value))
            sum(vals >= .y, na.rm = TRUE)
          }),
          triggered = nb_hour_bad_weather >= trigger_number_hours,
          sinistre = triggered * lump_sum_maintain
        )
      
      camping_compta <- donnees_completes %>% distinct(policy_code, .keep_all = TRUE)
      
      return(list(
        camping_compta = camping_compta,
        donnees_completes = donnees_completes
      ))
  })


   

  # Fichiers précédents filtré sur les dates mises en input et le nom du partenaire
  donnees_completes_filtre_partenaire <- reactive({
    data <- subset(donnees()$donnees_completes,
               company_name == input$camping_partenaire_id)
    return(data)
  })

  donnees_completes_filtre_partenaire_dates <- reactive({
    # Show loading screen when filtering data
    waiter_screen <- waiter::Waiter$new(
      id = c("tableau_precipitations_camping", "liste_camping_precipitations",
             "globales_campings", "liste_clients_precipitations_global",
             "globales_campings_journalier", "liste_clients_precipitations_global_journalier",
             "franceMap"),
      html = waiter::spin_fading_circles(),
      color = waiter::transparent(.5)
    )
    waiter_screen$show()

    data <- subset(donnees()$donnees_completes,
                   trip_day >= input$date_range_camping_id[1] &
                     trip_day <= input$date_range_camping_id[2] &
                     company_name == input$camping_partenaire_id)

    waiter_screen$hide()
    return(data)
  })
  
  clients_filtres_partenaire <- reactive({
    data <- subset(donnees()$camping_compta,
                   company_name == input$camping_partenaire_id)
    return(data)
  })
  
  clients_filtres_partenaire_dates <- reactive({
    # Show loading screen when filtering client data
    waiter_screen <- waiter::Waiter$new(
      id = c("Nombre_de_clients", "Nombre_journees_couvertes", "Somme_assuree",
             "Graph_clients_par_jour", "Graph_somme_assuree_par_jour", "liste_clients"),
      html = waiter::spin_fading_circles(),
      color = waiter::transparent(.5)
    )
    waiter_screen$show()

    data <- subset(donnees()$camping_compta,
                   trip_effective_end_date >= input$date_range_camping_id[1] &
                     trip_effective_start_date <= input$date_range_camping_id[2] &
                     company_name == input$camping_partenaire_id)

    waiter_screen$hide()
    return(data)
  })
  
  ############################################################################### ONGLET Sommes assurées
  
  # Nombre de clients unique
  output$Nombre_de_clients <- renderText({
    data <- clients_filtres_partenaire_dates()
    format(nrow(data), big.mark = " ", scientific = FALSE)
  })
  
  # Nombre de journées assurés
  nb_jours <- reactive({
    data <- clients_filtres_partenaire_dates()
    start <- pmax(data$trip_effective_start_date, input$date_range_camping_id[1])
    end   <- pmin(data$trip_effective_end_date,   input$date_range_camping_id[2])
    sum(as.numeric(end - start + 1))
  })
  
  output$Nombre_journees_couvertes <- renderText({
    data <- nb_jours()
    format(data, big.mark = " ", scientific = FALSE)
  })
  
  # Montant total assuré
  somme_assuree <- reactive({
    data <- clients_filtres_partenaire_dates()
    start <- pmax(data$trip_effective_start_date, input$date_range_camping_id[1])
    end <- pmin(data$trip_effective_end_date,   input$date_range_camping_id[2])
    durees <- as.numeric(end - start + 1)
    round(sum(data$lump_sum_maintain * durees, na.rm = TRUE),0)
  })
  
  output$Somme_assuree <- renderText({
    data <- somme_assuree()
    format(data, big.mark = " ", scientific = FALSE)
  })
  
  
  # Liste des jours dans la plage de date choisie
  jours <- reactive({
    seq(from = input$date_range_camping_id[1],
        to   = input$date_range_camping_id[2],
        by   = "day")
  })
  
  # Nombre de client par jour pour chaque jour de la liste précédente
  clients_journalier <- reactive({
    df <- clients_filtres_partenaire_dates()
    jours_vec <- jours()
    nb <- vapply(jours_vec, function(j) {
      sum(df$trip_effective_start_date <= j & df$trip_effective_end_date   >= j)
      }, integer(1))
    
    data <- data.frame(
      jour = jours(),
      nb_clients = nb
    )
  })

  output$Graph_clients_par_jour <- highcharter::renderHighchart({
    hchart(clients_journalier(), "column", hcaes(y = nb_clients, x = jour))
  })
  
  # Somme assurée par jour pour chaque jour de la liste précédente
  somme_journaliere <- reactive({
    df <- clients_filtres_partenaire_dates()
    jours_vec <- jours()
    somme <-vapply(jours_vec, function(j) {
      present <- df$trip_effective_start_date <= j & df$trip_effective_end_date >= j
      sum(df$lump_sum_maintain[present]) # Ajouter na.rm = TRUE si problème de données manquantes
    }, numeric(1))
    data.frame(
      jour = jours(),
      somme_assurée = round(somme,0)
    )
  })

  output$Graph_somme_assuree_par_jour <- highcharter::renderHighchart({
    hchart(somme_journaliere(), "column", hcaes(y = somme_assurée, x = jour))
  })

  # Tableau des données client
  output$liste_clients <- DT::renderDataTable({
    data <- clients_filtres_partenaire_dates()
    data$trip_effective_start_date <- format(data$trip_effective_start_date,"%d-%m-%Y")
    data$trip_effective_end_date <- format(data$trip_effective_end_date,"%d-%m-%Y")
    datatable(
      data[,c("company_name", "activity_name","first_name","last_name","email", "reservation_number","policy_code",
              "uw_date","activity_start_date","activity_end_date", "preferred_language","client_country",
              "partner_id","combined_address", "weather_guarantee_type","activity_price","activity_category",
              "trip_effective_start_date","trip_effective_end_date","lump_sum_maintain")
      ],
      options = list(
        scrollX = TRUE,
        pageLength = 100,
        scrollY = "400px", 
        autoWidth = TRUE
      ),
      rownames = FALSE,
      class = 'compact stripe hover'
    )
  })

  ############################################################################### Onglet Filtre par numéro de réservation
  
  # Résumer des informations client du numéro de réservation choisi
  output$donnees_client <- renderUI({
    data <- clients_filtres_partenaire()
    ligne <- data[data$reservation_number == input$num_resa,]
    
    # Vérification que le numéro de contrat existe bien et est sur les dates données
    if (nrow(ligne) == 0) {
      return("Numéro de réservation introuvable")
    }
    
    # Formatage des données faciliter la lecture des dates
    ligne$trip_effective_start_date <- format(ligne$trip_effective_start_date, "%d-%m-%Y")
    ligne$trip_effective_end_date <- format(ligne$trip_effective_end_date, "%d-%m-%Y")
    
    HTML(paste("Résumé des infos du client:","<br>","<br>",
          "Nom du camping : ",ligne$activity_name,"<br>",
          "Prenom : ", ligne$first_name,"<br>",
          "Nom de famille : ", ligne$last_name,"<br>",
          "Premier jour de couverture : ", ligne$trip_effective_start_date,"<br>",
          "Dernier jour de couverture : ", ligne$trip_effective_end_date, "<br>",
          "Latitude : ", ligne$latitude, "<br>",
          "Longitude : ", ligne$longitude, "<br>",
          "Adresse du camping : ", ligne$combined_address
          ))
  })
  
  # Récupération du policy_id du numéro de réservation choisie (va servir de filtre)
  policy_id_target <- reactive({
    clients_filtres_partenaire() %>%
    filter(reservation_number == input$num_resa) %>%
    pull(policy_id)
  })

  # Tableau de précipitation du numéro de reservation choisis
  precipitation_client <- reactive({
    req(donnees_completes_filtre_partenaire(), policy_id_target())
    data <- donnees_completes_filtre_partenaire()
    pid  <- policy_id_target()

    if (pid == ""){
      return(data.table())
    } else {
      donnees_long <- data %>%
        filter(policy_id == pid) %>%
        select(trip_day, weather_data) %>%
        mutate(
          parsed = map(weather_data, ~ {
              .x$hour <- as.integer(str_sub(.x$forecast_hour, 1, 2))
              .x$value <- as.numeric(gsub(",", ".", .x$value))
              return(.x %>% select(hour, value))
          })
        ) %>%
        unnest(parsed)
      
      donnees_wide <- donnees_long %>%
        pivot_wider(
          names_from = hour,
          values_from = value,
          values_fill = list(value = NA)
        )
      
      run_hours <- donnees_completes_filtre_partenaire() %>%
        filter(policy_id == pid) %>%
        select(trip_day, date_run_utc, claim_id, nb_hour_bad_weather, triggered, sinistre)
      
      precip_final <- left_join(donnees_wide, run_hours, by = "trip_day")
      precip_final <- precip_final %>% select(-c("weather_data"))
      return(precip_final)
    }
  })
  
  output$tableau_precipitations <- renderUI({
    data <- precipitation_client()
    if (nrow(data) == 0){
      return("Données météos inexistantes pour ce numéro de contrat")
    } else {
      datatable(
        data,
        extensions = 'Buttons',
        options = list(
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print'),
          scrollX = TRUE,
          scrollY = "400px",
          pageLength = 100,
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })

  output$liste_clients_precipitations <- renderUI({
    data <- donnees_completes_filtre_partenaire()
    data <- data %>% filter (reservation_number == input$num_resa)
    if (nrow(data) == 0){
      return("Pas de données météorologiques pour ce numéro de réservation")
    } else {
      datatable(
        data[,c("trip_day","company_name", "activity_name","first_name","last_name","email", "reservation_number","policy_code",
                "uw_date","activity_start_date","activity_end_date", "preferred_language","client_country",
                "partner_id","combined_address", "weather_guarantee_type","activity_price","activity_category",
                "trip_effective_start_date","trip_effective_end_date","trigger_hour_inf","trigger_number_hours",
                "nb_hour_bad_weather","triggered","claim_id","lump_sum_maintain","sinistre")
        ],
          options = list(
            scrollX = TRUE,
            pageLength = 100,
            scrollY = "400px",
            autoWidth = TRUE
          ),
          rownames = FALSE,
          class = 'compact stripe hover'
        )
    }
  })
  
  ############################################################################### Onglet Filtre par policy_code
   
  # Résumer des informations client du policy_code choisi
  output$donnees_client_pc <- renderUI({
    data <- clients_filtres_partenaire()
    ligne <- data[data$policy_code == input$policy_code,]
    
    # Vérification que le numéro de contrat existe bien et est sur les dates données
    if (nrow(ligne) == 0) {
      return("Numéro de réservation introuvable")
    }
    
    # Formatage des données faciliter la lecture des dates
    ligne$trip_effective_start_date <- format(ligne$trip_effective_start_date, "%d-%m-%Y")
    ligne$trip_effective_end_date <- format(ligne$trip_effective_end_date, "%d-%m-%Y")
    
    HTML(paste("Résumé des infos du client:","<br>","<br>",
               "Nom du camping : ",ligne$activity_name,"<br>",
               "Prenom : ", ligne$first_name,"<br>",
               "Nom de famille : ", ligne$last_name,"<br>",
               "Premier jour de couverture : ", ligne$trip_effective_start_date,"<br>",
               "Dernier jour de couverture : ", ligne$trip_effective_end_date, "<br>",
               "Latitude : ", ligne$latitude, "<br>",
               "Longitude : ", ligne$longitude, "<br>",
               "Adresse du camping : ", ligne$combined_address
    ))
  })
  
  # Récupération du policy_id du numéro de réservation choisie (va servir de filtre)
  policy_id_target_pc <- reactive({
    clients_filtres_partenaire() %>%
      filter(policy_code == input$policy_code) %>%
      pull(policy_id)
  })
  
  # Tableau de précipitation du numéro de reservation choisis
  precipitation_client_pc <- reactive({
    req(donnees_completes_filtre_partenaire(),policy_id_target_pc())
    data <- donnees_completes_filtre_partenaire()
    pid_pc  <- policy_id_target_pc()
    
    if (pid_pc == ""){
      return(data.table())
    } else {
      donnees_long <- data %>%
        filter(policy_id == pid_pc) %>%
        select(trip_day, weather_data) %>%
        mutate(
          parsed = map(weather_data, ~ {
            .x$hour <- as.integer(str_sub(.x$forecast_hour, 1, 2))
            .x$value <- as.numeric(gsub(",", ".", .x$value))
            return(.x %>% select(hour, value))
          })
        ) %>%
        unnest(parsed)
      
      donnees_wide <- donnees_long %>%
        pivot_wider(
          names_from = hour,
          values_from = value,
          values_fill = list(value = NA)
        )
      
      run_hours <- donnees_completes_filtre_partenaire() %>%
        filter(policy_id == pid_pc) %>%
        select(trip_day, date_run_utc, claim_id, nb_hour_bad_weather, triggered, sinistre)
      
      precip_final <- left_join(donnees_wide, run_hours, by = "trip_day")
      precip_final <- precip_final %>% select(-c("weather_data"))
      return(precip_final)
    }
  })
  
  output$tableau_precipitations_pc <- renderUI({
    data <- precipitation_client_pc()
    if (nrow(data) == 0){
      return("Données météos inexistantes pour ce numéro de contrat")
    } else {
      datatable(
        data,
        extensions = 'Buttons',
        options = list(
          dom = 'Bfrtip',
          buttons = c('copy', 'csv', 'excel', 'pdf', 'print'),
          scrollX = TRUE,
          scrollY = "400px",
          pageLength = 100,
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })
  
  output$liste_clients_precipitations_pc <- renderUI({
    data <- donnees_completes_filtre_partenaire()
    data <- data %>% filter (policy_code == input$policy_code)
    if (nrow(data) == 0){
      return("Pas de données météorologiques pour ce numéro de réservation")
    } else {
      datatable(
        data[,c("trip_day","company_name", "activity_name","first_name","last_name","email", "reservation_number","policy_code",
                 "uw_date","activity_start_date","activity_end_date", "preferred_language","client_country",
                 "partner_id","combined_address", "weather_guarantee_type","activity_price","activity_category",
                 "trip_effective_start_date","trip_effective_end_date","trigger_hour_inf","trigger_number_hours",
                 "nb_hour_bad_weather","triggered","claim_id","lump_sum_maintain","sinistre")
        ],
        options = list(
          scrollX = TRUE,
          pageLength = 100,
          scrollY = "400px",
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })
  
  ############################################################################### ONGLET Filtre par camping
  
  # Choix du camping à analyser et input réactif
  propositions_nom_campings <- function() {
    data <- clients_filtres_partenaire_dates()
    data <- data %>% filter(trip_day >= input$date_range_camping_id[1] & trip_day <= input$date_range_camping_id[2])
    unique(data$activity_name)
  }
  
  observeEvent(clients_filtres_partenaire_dates(), {
    choices <- propositions_nom_campings()
    selected_val <- isolate(input$nom_camping)
    selected_val <- if (!is.null(selected_val) && selected_val %in% choices) selected_val else NULL
    updateSelectizeInput(
      session,
      inputId = "nom_camping",
      choices = choices,
      selected = selected_val,
      server = TRUE
    )
  }, ignoreInit = FALSE)
  
  output$infos_camping <- renderUI({
    req(input$nom_camping)
    data <- clients_filtres_partenaire_dates()
    ligne <- data[data$activity_name == input$nom_camping,]
    ligne <- ligne %>% distinct(activity_name, .keep_all = TRUE)
    
    HTML(paste("Résumé des infos du camping:","<br>","<br>",
               "Nom du camping : ",ligne$activity_name,"<br>",
               "Latitude : ", ligne$latitude, "<br>",
               "Longitude : ", ligne$longitude, "<br>",
               "Adresse du camping : ", ligne$combined_address
    ))
  })
  
  # Tableau de précipitation du camping choisis
  precipitation_camping <- reactive({
    req(donnees_completes_filtre_partenaire_dates())

    # Show loading screen for precipitation data processing
    waiter_screen <- waiter::Waiter$new(
      id = "tableau_precipitations_camping",
      html = tagList(
        waiter::spin_fading_circles(),
        h4("Traitement des données météorologiques...", style = "color: white;")
      ),
      color = waiter::transparent(.5)
    )
    waiter_screen$show()

    data <- donnees_completes_filtre_partenaire_dates()
    nom_camping  <- input$nom_camping
    if (nrow(data %>%filter(activity_name == nom_camping)) == 0){
      waiter_screen$hide()
      return(data.table())
    } else {
    donnees_long <- data %>%
      filter(activity_name == nom_camping) %>%
      distinct(trip_day, .keep_all = TRUE) %>%
      select(trip_day, weather_data) %>%
      mutate(
        parsed = map(weather_data, ~ {
          .x$hour <- as.integer(str_sub(.x$forecast_hour, 1, 2))
          .x$value <- as.numeric(gsub(",", ".", .x$value))
          return(.x %>% select(hour, value))
        })
      ) %>%
      unnest(parsed)

    donnees_wide <- donnees_long %>%
      pivot_wider(
        names_from = hour,
        values_from = value,
        values_fill = list(value = NA)
      )

    run_hours <- donnees_completes_filtre_partenaire_dates() %>%
      filter(activity_name == nom_camping) %>%
      select(trip_day, date_run_utc, nb_hour_bad_weather, triggered, sinistre, lump_sum_maintain)

    precip_final <- donnees_wide %>%
      left_join(run_hours, by = "trip_day")

    precip_final <- precip_final %>%
      group_by(trip_day) %>%
      mutate(
        nb_clients = n(),
        somme_assurée = round(sum(lump_sum_maintain, na.rm = TRUE), 2),
        sinistres = round(sum(sinistre, na.rm = TRUE),2)
      ) %>%
      slice(1) %>%
      ungroup()
    precip_final <- precip_final %>% select(-c(lump_sum_maintain,sinistre,weather_data))

    waiter_screen$hide()
    return(precip_final)
    }
  })
  
  output$tableau_precipitations_camping <- renderUI({
    data <- precipitation_camping()
    if (nrow(data) == 0){
      return("Données météos inexistantes pour ce camping sur ces dates")
    } else {
      datatable(
        data,
        options = list(
          scrollX = TRUE,
          pageLength = 100,
          scrollY = "400px",
          autoWidth = TRUE
        ),
        rownames = FALSE, 
        class = 'compact stripe hover'
      )
    }
  })
  
  output$liste_camping_precipitations <- renderUI({
    data <- donnees_completes_filtre_partenaire_dates()
    data <- data %>% filter (activity_name == input$nom_camping)
    if (nrow(data) == 0){
      return("Données météos inexistantes pour ce camping sur ces dates")
    } else {
      data <- data %>%
        group_by(trip_day) %>%
        mutate(
          nb_clients = n(),
          somme_assurée = round(sum(lump_sum_maintain, na.rm = TRUE), 2),
          sinistres = round(sum(sinistre, na.rm = TRUE),2)
        ) %>%
        slice(1) %>%
        ungroup()
      datatable(
        data[,c("trip_day","company_name", "activity_name", "nb_clients","somme_assurée",
                "partner_id","combined_address", "weather_guarantee_type","activity_category",
                "trigger_hour_inf","trigger_number_hours",
                "nb_hour_bad_weather","triggered","sinistres")
        ],
        options = list(
          scrollX = TRUE,
          pageLength = 100,
          scrollY = "400px",
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })
  
  ############################################################################### Onglet Recap global
  
  # Tableau résumé du nombre de campings/clients sinistrés ou non, et le montant total assuré
  output$couverture <- renderUI({
    if (input$date_range_camping_id[1] > Sys.Date()){
      return(print("Date future choisie, merci de choisir au maximum la date d'aujourd'hui"))
    } else {
      data <- donnees_completes_filtre_partenaire_dates()
      if (nrow(data) == 0){
        return("Aucun trip_day trouvé sur cette date")
      } else {
        # Fonction d’aide pour filtrer et agréger
        n_campings <- function(df) length(unique(df$activity_name))
        n_clients  <- function(df) length(unique(df$policy_code))
        sum_assure <- function(df) sum(df$lump_sum_maintain, na.rm = TRUE)
        
        # Sous-ensembles
        data_sinistre     <- subset(data, triggered)
        data_non_sinistre <- subset(data, !is.na(triggered) & !triggered)
        
        # Calculs
        df <- data.frame(
          "Non sinistré" = c(
            n_campings(data_non_sinistre),
            n_clients(data_non_sinistre),
            round(sum_assure(data_non_sinistre),0)
          ),
          "Sinistré" = c(
            n_campings(data_sinistre),
            n_clients(data_sinistre),
            round(sum_assure(data_sinistre),0)
          ),
          "Total" = c(
            n_campings(data_non_sinistre)+n_campings(data_sinistre),
            n_clients(data_non_sinistre)+n_clients(data_sinistre),
            round(sum_assure(data),0)
          ),
          row.names = c("Nombre de campings", "Nombre de clients", "Somme assurée")
        )
        
        datatable(
          df,
          options = list(dom = 't'),
          rownames = TRUE,
          class = 'compact stripe hover'
        )
      }
    }
  })
  
    output$couverture_unique <- renderUI({
    if (input$date_range_camping_id[1] <= Sys.Date()){
      data <- donnees_completes_filtre_partenaire_dates()
      if (nrow(data) == 0){
        return("")
      } else {
        HTML(paste("Nombre de campings unique assurés : ", length(unique(data$activity_name)), "<br>",
                   "Nombre de clients unique assurés : ", length(unique(data$reservation_number)), "<br>"
        ))
      }
    }
  })
  
  #Tableau global pour tous les campings couverts sur la période choisie
  output$globales_campings <- renderUI({
    data <- donnees_completes_filtre_partenaire_dates()
    if (nrow(data) == 0){
      return("Aucun trip_day trouvé sur cette date")
    } else {
      df <- data %>%
        group_by(activity_name, trip_day) %>%
        mutate(
          nb_clients = n(),
          somme_assurée = round(sum(lump_sum_maintain, na.rm = TRUE), 2),
          sinistres = round(sum(sinistre, na.rm = TRUE),2)
        ) %>%
        slice(1) %>%
        ungroup()
      datatable(
        df[,c("trip_day","activity_name","nb_clients","somme_assurée","date_run_utc","trigger_hour_inf","trigger_number_hours",
              "nb_hour_bad_weather","triggered","sinistres")],
        options = list(
          scrollX = TRUE,
          pageLength = 100,
          scrollY = "400px",
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })
  
  # Tablau global pour tous les clients couverts de la période choisie
  output$liste_clients_precipitations_global <- renderUI ({
    data <- donnees_completes_filtre_partenaire_dates()
    if (nrow(data) == 0){
      return("Aucun trip_day trouvé sur ces dates")
    } else {
      datatable(
        data[,c("trip_day","company_name","activity_name","last_name","first_name","email","reservation_number","policy_id","policy_code",
                "trigger_hour_inf","trigger_number_hours","nb_hour_bad_weather","triggered","lump_sum_maintain","claim_amount",
                "uw_date","activity_start_date","activity_end_date","preferred_language","client_country","combined_address",
                "trip_effective_start_date","trip_effective_end_date","weather_guarantee_type","activity_price",
                "activity_category","partner_id")
        ],
        options = list(
          scrollX = TRUE,
          pageLength = 100,
          scrollY = "400px",
          autoWidth = TRUE
        ),
        rownames = FALSE,
        class = 'compact stripe hover'
      )
    }
  })
    
    ############################################################################# ONGLET Recap par jour
    
    output$date_debut <- renderUI({
      format(input$date_range_camping_id[1],"%d-%m-%Y")
    })
    
    # Tableau résumé du nombre de campings/clients sinistrés ou non, et le montant total assuré
    output$couverture_journaliere <- renderUI({
      if (input$date_range_camping_id[1] > Sys.Date()){
        return(print("Date future choisie, merci de choisir au maximum la date d'aujourd'hui"))
      } else {
        data <- donnees_completes_filtre_partenaire_dates()
        data <- data %>% filter(trip_day == input$date_range_camping_id[1])
        if (nrow(data) == 0){
          return("Aucun trip_day trouvé sur cette date")
        } else {
          # Fonction d’aide pour filtrer et agréger
          n_campings <- function(df) length(unique(df$activity_name))
          n_clients  <- function(df) length(unique(df$reservation_number))
          sum_assure <- function(df) sum(df$lump_sum_maintain, na.rm = TRUE)
          
          # Sous-ensembles
          data_sinistre     <- subset(data, triggered)
          data_non_sinistre <- subset(data, !is.na(triggered) & !triggered)
          
          # Calculs
          df <- data.frame(
            "Non sinistré" = c(
              n_campings(data_non_sinistre),
              n_clients(data_non_sinistre),
              round(sum_assure(data_non_sinistre),0)
            ),
            "Sinistré" = c(
              n_campings(data_sinistre),
              n_clients(data_sinistre),
              round(sum_assure(data_sinistre),0)
            ),
            "Total" = c(
              n_campings(data_non_sinistre)+n_campings(data_sinistre),
              n_clients(data_non_sinistre)+n_clients(data_sinistre),
              round(sum_assure(data),0)
            ),
            row.names = c("Nombre de campings", "Nombre de clients", "Somme assurée")
          )
          
          datatable(
            df,
            options = list(dom = 't'),
            rownames = TRUE,
            class = 'compact stripe hover'
          )
        }
      }
    })
    
    #Tableau global pour tous les campings couverts sur la période choisie
    output$globales_campings_journalier <- renderUI({
      data <- donnees_completes_filtre_partenaire_dates()
      data <- data %>% filter(trip_day == input$date_range_camping_id[1])
      if (nrow(data) == 0){
        return("Aucun trip_day trouvé sur cette date")
      } else {
        df <- data %>%
          group_by(activity_name, trip_day) %>%
          mutate(
            nb_clients = n(),
            somme_assurée = round(sum(lump_sum_maintain, na.rm = TRUE), 2),
            sinistres = round(sum(sinistre, na.rm = TRUE),2)
          ) %>%
          slice(1) %>%
          ungroup()
        datatable(
          df[,c("trip_day","activity_name","nb_clients","somme_assurée","date_run_utc","trigger_hour_inf","trigger_number_hours",
                  "nb_hour_bad_weather","triggered","sinistres")],
          options = list(
            scrollX = TRUE,
            pageLength = 100,
            scrollY = "400px",
            autoWidth = TRUE
          ),
          rownames = FALSE,
          class = 'compact stripe hover'
        )
      }
    })
    
    # Tablau global pour tous les clients couverts de la période choisie
    output$liste_clients_precipitations_global_journalier <- renderUI ({
      data <- donnees_completes_filtre_partenaire_dates()
      data <- data %>% filter(trip_day == input$date_range_camping_id[1])
      if (nrow(data) == 0){
        return("Aucun trip_day trouvé sur ces dates")
      } else {
        datatable(
          data[,c("trip_day","company_name","activity_name","last_name","first_name","email","reservation_number","policy_id","policy_code",
                  "trigger_hour_inf","trigger_number_hours","nb_hour_bad_weather","triggered","lump_sum_maintain","claim_amount",
                  "uw_date","activity_start_date","activity_end_date","preferred_language","client_country","combined_address",
                  "trip_effective_start_date","trip_effective_end_date","weather_guarantee_type","activity_price",
                  "activity_category","partner_id")
          ],
          options = list(
            scrollX = TRUE,
            pageLength = 100,
            scrollY = "400px",
            autoWidth = TRUE
          ),
          rownames = FALSE,
          class = 'compact stripe hover'
        )
      }
    })

    points_campings_pour_carte <- reactive({
      data <- donnees_completes_filtre_partenaire_dates()
      data <- data %>% filter(trip_day == input$date_range_camping_id[1])
      if (nrow(data) == 0){
        return(data.table())
      } else {
        data <- data %>%
          group_by(activity_name, trip_day) %>%
          mutate(
            nb_clients = n(),
            somme_assurée = round(sum(lump_sum_maintain, na.rm = TRUE), 2),
            sinistres = round(sum(sinistre, na.rm = TRUE),2)
          ) %>%
          slice(1) %>%
          ungroup()
        data <- data %>%
          mutate(color = ifelse(triggered, "blue", "red")) %>%
          select(latitude,longitude,activity_name,color,trigger_number_hours,nb_hour_bad_weather,somme_assurée,sinistres)
        return(data)
      }
    })
    
    output$franceMap <- renderLeaflet({
      data_points <- points_campings_pour_carte()

      if (nrow(data_points) == 0) {
        return(leaflet() %>%
                 addTiles() %>%
                 setView(lng = 2.2137, lat = 46.2276, zoom = 6))
      } else {
        return(leaflet() %>%
                 addTiles() %>%
                 setView(lng = 2.2137, lat = 46.2276, zoom = 6) %>%
                 addCircleMarkers(
                   data = data_points,
                   lng = ~longitude,
                   lat = ~latitude,
                   label = ~paste0(
                     "<b>Camping:</b> ", activity_name, "<br/>",
                     "<b>Seuil d'heures:</b> ", trigger_number_hours, "<br/>",
                     "<b>Nombre d'heures de pluies:</b> ", nb_hour_bad_weather, "<br/>",
                     "<b>Somme assurée:</b> ", somme_assurée, " €", "<br/>",
                     "<b>Sinistres:</b> ", sinistres, " €"
                   ) %>% lapply(htmltools::HTML),
                   color = ~color,
                   fillOpacity = 0.9,
                   radius = 6,
                   stroke = FALSE,
                   labelOptions = labelOptions(
                     direction = "auto",
                     textsize = "13px",
                     style = list("font-weight" = "normal", "padding" = "3px 8px")
                   )
                 ))
      }
    })

    output$legende_carte <- renderUI({
      HTML(paste("Points rouges: campings non sinistrés", "<br>",
                 "Points bleus: campings sinistrés"))
    })
    
    
  }