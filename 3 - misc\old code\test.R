
df_underwriting <- readRDS("www/df_underwriting.rds")

df_merge <- merge(setDT(df_underwriting), setDT(df_claims), by = "policy_id", all.x = TRUE)

# Replace NA values in amount_of_claims with 0 and create the frequency column
df_merge[, `:=`(amount_of_claims = ifelse(is.na(amount_of_claims), 0, amount_of_claims),
                frequency = ifelse(is.na(amount_of_claims), 0, 1))]



claims_amount_by_region_dept <-  df_merge %>% 
  dplyr::left_join(
    dep_corres %>% select(code_insee_dep,code_insee_reg), by = c("departement" = "code_insee_dep")
  ) %>% 
  dplyr::group_by(departement,code_insee_reg) %>%
  dplyr::summarise(TotalClaimAmount = sum(amount_of_claims)) 

data_reg <- claims_amount_by_region_dept %>% 
  group_by(code_insee_reg) %>% 
  dplyr::summarise(
    value = sum(TotalClaimAmount)
  ) %>% 
  left_join(reg_corres)

highchart(type = 'map') %>%
  hc_add_series(
    mapData = frgeoson_reg, data = data_reg, joinBy = c("hc-a2"),
    borderWidth = 0.1, borderColor = "#FAFAFA", dataLabels = list(enabled = TRUE, formatter = JS("function(){return(this.point.hc_dataLabel)}")),
    tooltip = list(
      useHTML = TRUE,
      headerFormat = "<p>",
      pointFormat = paste0("<b>{point.name_reg}</b><br>",
                           "<b style=\"color:#1874CD\">Claims amounts:</b> {point.value:.0f}<br>"
      ),
      footerFormat = "</p>"
    )) %>%
  hc_plotOptions(map = list(states = list(hover = list(color = '#03989E')))) %>%
  hc_colorAxis(minColor = brewer.pal(8,"Reds")[1], maxColor = brewer.pal(8,"Reds")[8]) %>%
  hc_legend(enabled = TRUE)




data_dep <- claims_amount_by_region_dept %>% 
  group_by(departement) %>% 
  dplyr::summarise(
    Claim_amt = sum(TotalClaimAmount)
  ) %>% 
  left_join(dep_corres, by = c('departement' = 'code_insee_dep'))


highchart(type = 'map') %>%
  hc_add_series(
    mapData = frgeoson_dep, data = data_dep, joinBy = c("hc-a2","hc_a2"),
    borderWidth = 0.1, borderColor = "#FAFAFA", dataLabels = list(enabled = TRUE, formatter = JS("function(){return(this.point.hc_dataLabel)}")),
    tooltip = list(
      useHTML = TRUE,
      headerFormat = "<p>",
      pointFormat = paste0("<b>{point.name}</b><br>",
                           "<b style=\"color:#1874CD\">Claims amounts:</b> {point.value:.0f}<br>"
      ),
      footerFormat = "</p>"
    )) %>%
  #hc_plotOptions(map = list(states = list(hover = list(color = '#03989E')))) %>%
  hc_colorAxis(min = 4302345, max = max(data_dep$value),minColor = brewer.pal(8,"Reds")[1], maxColor = brewer.pal(8,"Reds")[8]) %>%
  hc_legend(enabled = TRUE) %>% 
  hc_mapNavigation(enabled = TRUE)




  hcmap("countries/fr/fr-all-all",showInLegend = FALSE,data = data_dep, value = "Claim_amt",
        joinBy = c("hc-a2","hc_a2"), name = "Claims amount",
        dataLabels = list(enabled = TRUE, format = '{point.name}'),
        borderColor = "#FAFAFA", borderWidth = 0.1,
        tooltip = list(valueDecimals = 2, valuePrefix = "€", valueSuffix = " EURO")) %>%
        hc_colorAxis(min = min(data_dep$Claim_amt), max = max(data_dep$Claim_amt)) %>%
        hc_mapNavigation(enabled = TRUE)