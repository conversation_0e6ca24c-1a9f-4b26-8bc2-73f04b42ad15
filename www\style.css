.img-circle {
    border-radius: 50%;
	 width: 15%; /* Reduce image size to 50% */
     height: auto;
}


.skin-blue .left-side, .skin-blue .main-sidebar, .skin-blue .wrapper {
    background-color: #bb8d2a	;
}

 .skin-blue .main-header .logo {
                              background-color: #21388B;
                              }
							  
/* logo when hovered */
        .skin-blue .main-header .logo:hover {
                              background-color: #21388B;
                              }

        /* navbar (rest of the header) */
        .skin-blue .main-header .navbar {
                              background-color: #21388B;
                              }        

        /* main sidebar */
        .skin-blue .main-sidebar {
                              background-color: #21388B;
                              }

     
        /* other links in the sidebarmenu when hovered */
         .skin-blue .main-sidebar .sidebar .sidebar-menu a:hover{
                              background-color: #e97569;
                              }
        /* toggle button when hovered  */                    
         .skin-blue .main-header .navbar .sidebar-toggle:hover{
                              background-color: #e97569;
                              }

/* BUTTONS */
.btn-default {
    color: #ffffff;
    background-color: #2c3e50;
    border-color: #2c3e50
}

.btn-default:focus,.btn-default.focus {
    color: #ffffff;
    background-color: #233140;
    border-color: #233140
}

.btn-default:hover {
    color: #ffffff;
    background-color: #233140;
    border-color: #233140
}

.btn-default:active,.btn-default.active,.open>.dropdown-toggle.btn-default {
    color: #ffffff;
    background-color: #233140;
    border-color: #233140
}

.btn-default:active:hover,.btn-default.active:hover,.open>.dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open>.dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open>.dropdown-toggle.btn-default.focus {
    color: #ffffff;
    background-color: #233140;
    border-color: #233140
}

.btn-default:active,.btn-default.active,.open>.dropdown-toggle.btn-default {
    background-image: none
}

.btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus {
    background-color: #222222;
    border-color: #222222
}

.btn-default .badge {
    color: #222222;
    background-color: #ffffff
}

.btn-success {
    color: #ffffff;
    background-color: #18bc9c;
    border-color: #18bc9c
}

.btn-success:focus,.btn-success.focus {
    color: #ffffff;
    background-color: #16ab8e;
    border-color: #16ab8e
}

.btn-success:hover {
    color: #ffffff;
    background-color: #16ab8e;
    border-color: #16ab8e
}

.btn-success:active,.btn-success.active,.open>.dropdown-toggle.btn-success {
    color: #ffffff;
    background-color: #16ab8e;
    border-color: #16ab8e
}

.btn-success:active:hover,.btn-success.active:hover,.open>.dropdown-toggle.btn-success:hover,.btn-success:active:focus,.btn-success.active:focus,.open>.dropdown-toggle.btn-success:focus,.btn-success:active.focus,.btn-success.active.focus,.open>.dropdown-toggle.btn-success.focus {
    color: #ffffff;
    background-color: #16ab8e;
    border-color: #16ab8e
}

.bg-success{
    background-color: #18bc9c;
}

.bg-danger{
    background-color: #E31A1C;
}

.bg-warning{
    background-color: #FF7F00;
}

/* BUTTONS */
.text {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.text2 {
    color: #ffffff;
    background-color: #2fa4e7;
    border-color: #2fa4e7;
}


.title {
    margin: -10px -9px 0;
    padding: 10px;
    text-align: center;
}

.box.box-solid.box-primary>.box-header {
  background:#21388B;
                    }

.box.box-solid.box-primary{
   border: 2px solid #21388B;
}


.box.box-primary>.box-header .title {
          text-align: center;
        }
		

.box-header .box-title {
  display: inline-block;
  font-size: 18px;
  text-align: center;
  width: 100%;
  font-weight: bold; /* Make the text bold */
  text-transform: uppercase;
}