


# Set up database connection parameters
host <- "ow-preprod-api-db-postgres.c10qgu44y8w3.eu-west-3.rds.amazonaws.com"           # e.g., "localhost" or "your_host_name"
port <- 5432                  # Default PostgreSQL port
dbname <- "orakle_weather"      # e.g., "my_database"
user <- "yousouf"       # e.g., "my_username"
password <- "wJ4OPDML9k3Z9q1fPKKNDZLM562D"   # e.g., "my_password"

# Establish a connection to the PostgreSQL database
con <- dbConnect(RPostgres::Postgres(),
                 host = host,
                 port = port,
                 dbname = dbname,
                 user = user,
                 password = password)

# Check connection
print(con)

# Execute a query to retrieve data (replace 'your_table' with the actual table name)
query <- "SELECT * FROM claims"
data <- dbGetQuery(con, query)

# Print the retrieved data
print(data)

# Perform your data analysis or manipulation here
# For example, calculate cumulative sum of GWP by year and weekofyear
data <- data %>%
  group_by(year) %>%
  arrange(year, weekofyear) %>%
  mutate(cum_GWP = cumsum(GWP))

# Print the processed data
print(data)

# Close the connection
dbDisconnect(con)
