# Load all packages
library(shiny)
library(dplyr)
library(lubridate)
library(sass)
library(highcharter)
library(echarts4r)
library(shinyWidgets)
library(htmlwidgets)
library(shinyjs)
library(shinycssloaders)
library(waiter)
library(modules)
library(data.table)
library(leaflet)
library(shinycustomloader)
library(shinypivottabler)
library(fresh)
library(DBI)
library(RPostgres)
library(bs4Dash)
library(rlang)
library(zoo)
library(xts)
library(readxl)
library(plotly)
library(RColorBrewer)
library(rAmCharts)

### Arnaud
library(DT)
library(jsonlite)
library(tidyr)
library(purrr)
library(stringr)
library(tibble)
library(tools)
library(RSQLite)
library(httr)
library(rjson)

Sys.setlocale("LC_TIME", "English")
