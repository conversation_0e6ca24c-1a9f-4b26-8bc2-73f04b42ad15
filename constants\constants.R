import("shiny")
import("highcharter")
import("fresh")

APP_TITLE <- "Pricing tools"
APP_LAST_UPDATE <- as.Date("2024-05-13")
APP_VERSION <- "1.0.0"

orakle_weather_website <- "https://orakleweather.com/"

Colors <- list(
  write = '#FFF',
  black = '#0a1e2B',
  first = "#133EEA",
  second = "#fff9c0",
  third = "#e97569",
  fourth = "#21388B",
  others_1 = "#285b80",
  others_2 = "#B3B8BA"
)


orakeWeather_logo <- tags$a(
  href = orakle_weather_website,
  target = "_blank",
  class = "logo-link",
  img(src='images/orakleweather_logo.jpg', class = "img-circle", alt = "Orakke weather Logo")
)


list_select <- list(
  type_of_bussiness = c("Outdoor escape game","Boat rental - cruise","Tourism/travel accommodation","Cycling holidays",
                        "Boat rental - event","Fireworks/drone shows","Amusement park","Event"),
  type_of_trigger = c("Cumulated", "Per hour"),
  peril_type =  c("Rain", "Wind", "Rain and Wind"),
  choice_for_cells = c("8 cells", "0 cells")
)

### Theme shinypivottabler color Orakle Weather
theme <- list(
  headerBackgroundColor = "#21388B",
  headerColor = "rgb(255, 255, 255)",
  cellBackgroundColor = "rgb(255, 255, 255)",
  cellColor = "rgb(0, 0, 0)",
  outlineCellBackgroundColor = "rgb(192, 192, 192)",
  outlineCellColor = "rgb(0, 0, 0)",
  totalBackgroundColor = "#e6e6e6",
  totalColor = "rgb(0, 0, 0)",
  borderColor = "#000000"
)


### Dashboard
bs4DashTheme <- bs4Dash_theme(
  primary = "#21388B",
  secondary = "#e97569",
  success = "#A3BE8C",
  danger = "#dc3545",
  "sidebar-light-bg" = "#3B4252",
  "sidebar-light-color" = "#E5E9F0",
  "main-bg" = "#2E3440",
  "body-color" = "#ECEFF4",
  "card-bg" = "#4C566A", # bs4Card() background
  "white" = "#E5E9F0",
  "info-box-bg" = "#4C566A",  # bs4InfoBox() background
  dark = "#272c30", #  bs4DashNavbar(status = "dark") background,
  "gray-600" = "#FFF"
)

